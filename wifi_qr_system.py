import tkinter as tk
from tkinter import ttk, messagebox
import qrcode
from PIL import Image, ImageTk
import io
import base64
from enhanced_db import Database
import datetime

class WiFiQRSystem:
    def __init__(self, root, customer_id=None, booking_id=None):
        self.root = root
        self.customer_id = customer_id
        self.booking_id = booking_id
        self.db = Database('hq_logistics.db')
        
        self.root.title("HUB Guest House - WiFi Access System")
        self.root.geometry("800x900")
        self.root.configure(bg="#f0f8ff")
        
        # Colors
        self.colors = {
            'navy': '#1e3a8a',
            'light_navy': '#3b82f6',
            'success': '#10b981',
            'warning': '#f59e0b',
            'white': '#ffffff',
            'light_gray': '#f8fafc'
        }
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the WiFi QR system interface"""
        # Header
        header_frame = tk.Frame(self.root, bg=self.colors['navy'], height=100)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="🌐 WiFi Access System", 
                font=("Arial", 24, "bold"), fg="white", bg=self.colors['navy']).pack(pady=20)
        
        # Main content
        main_frame = tk.Frame(self.root, bg="#f0f8ff")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Welcome section
        welcome_frame = ttk.LabelFrame(main_frame, text="🎉 Welcome to HUB Guest House WiFi", padding=15)
        welcome_frame.pack(fill=tk.X, pady=10)
        
        welcome_text = """🌟 Exclusive WiFi Access for Our Valued Guests!

✅ High-Speed Internet Access (100 Mbps)
✅ Secure WPA2 Protected Network
✅ 24/7 Technical Support
✅ Multiple Device Support (up to 2 devices)
✅ Valid for your entire stay duration

📱 Download our mobile app for more exclusive services:
   • Online room booking
   • Restaurant orders
   • Gym membership
   • Event notifications
   • Loyalty rewards program"""
        
        tk.Label(welcome_frame, text=welcome_text, justify=tk.LEFT, 
                font=("Arial", 11), fg=self.colors['navy']).pack(anchor=tk.W)
        
        # QR Code generation section
        qr_frame = ttk.LabelFrame(main_frame, text="🔐 Your WiFi Access QR Code", padding=15)
        qr_frame.pack(fill=tk.X, pady=10)
        
        # Generate QR button
        generate_frame = tk.Frame(qr_frame)
        generate_frame.pack(fill=tk.X, pady=10)
        
        tk.Button(generate_frame, text="🎫 Generate My WiFi Access Code", 
                 font=("Arial", 14, "bold"), bg=self.colors['success'], fg="white",
                 command=self.generate_wifi_qr, padx=20, pady=10).pack()
        
        # QR Code display area
        self.qr_display_frame = tk.Frame(qr_frame, bg="white", relief=tk.RIDGE, bd=2)
        self.qr_display_frame.pack(fill=tk.X, pady=20)
        
        # Instructions section
        instructions_frame = ttk.LabelFrame(main_frame, text="📋 How to Connect", padding=15)
        instructions_frame.pack(fill=tk.X, pady=10)
        
        instructions_text = """📱 EASY CONNECTION STEPS:

1️⃣ Open your phone's camera or QR code scanner
2️⃣ Point camera at the QR code above
3️⃣ Tap the WiFi notification that appears
4️⃣ Your device will automatically connect!

💡 Alternative Manual Connection:
   • Network Name (SSID): HUB_GUEST_WIFI
   • Password: [Will be shown after QR generation]
   • Security: WPA2

🎁 BONUS: Scan this QR code to also earn loyalty points!
   • 5 points for WiFi access
   • 20 points for downloading our app
   • 50 points for room bookings
   • Redeem points for discounts and free services!"""
        
        tk.Label(instructions_frame, text=instructions_text, justify=tk.LEFT, 
                font=("Arial", 10), fg=self.colors['navy']).pack(anchor=tk.W)
        
        # App download promotion
        app_frame = ttk.LabelFrame(main_frame, text="📱 Download Our Mobile App", padding=15)
        app_frame.pack(fill=tk.X, pady=10)
        
        app_text = """🚀 Get the HUB Guest House Mobile App for:

🏨 Easy room booking from anywhere
🍽️ Order food directly to your room
🏋️ Book gym sessions
📅 View events and announcements
🎁 Exclusive member discounts
💰 Loyalty rewards tracking
🌐 Instant WiFi access codes

Available on Google Play Store and Apple App Store
Search: "HUB Guest House Command Facilities"

🎯 Use referral code: WIFI2024 for bonus points!"""
        
        tk.Label(app_frame, text=app_text, justify=tk.LEFT, 
                font=("Arial", 10), fg=self.colors['navy']).pack(anchor=tk.W)
        
        # Action buttons
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=20)
        
        tk.Button(buttons_frame, text="📧 Email QR Code", 
                 command=self.email_qr_code, bg=self.colors['light_navy'], 
                 fg="white", padx=15, pady=5).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="📱 SMS Instructions", 
                 command=self.sms_instructions, bg=self.colors['warning'], 
                 fg="white", padx=15, pady=5).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="🖨️ Print QR Code", 
                 command=self.print_qr_code, bg=self.colors['navy'], 
                 fg="white", padx=15, pady=5).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="🏠 Back to Booking", 
                 command=self.root.destroy, bg="gray", 
                 fg="white", padx=15, pady=5).pack(side=tk.RIGHT, padx=5)
    
    def generate_wifi_qr(self):
        """Generate WiFi QR code for the customer"""
        try:
            if not self.customer_id:
                messagebox.showerror("Error", "Customer ID required for WiFi access")
                return
            
            # Generate WiFi access code
            wifi_data = self.db.generate_wifi_access_code(
                customer_id=self.customer_id,
                booking_id=self.booking_id,
                access_type="Guest",
                duration_hours=48  # 48 hours access
            )
            
            # Clear previous QR display
            for widget in self.qr_display_frame.winfo_children():
                widget.destroy()
            
            # Create QR code image
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=8,
                border=4,
            )
            
            wifi_qr_data = f"WIFI:T:WPA;S:{wifi_data['ssid']};P:{wifi_data['wifi_password']};H:false;;"
            qr.add_data(wifi_qr_data)
            qr.make(fit=True)
            
            # Create QR code image
            qr_img = qr.make_image(fill_color="black", back_color="white")
            qr_img = qr_img.resize((300, 300), Image.LANCZOS)
            
            # Convert to PhotoImage
            qr_photo = ImageTk.PhotoImage(qr_img)
            
            # Display QR code
            qr_label = tk.Label(self.qr_display_frame, image=qr_photo, bg="white")
            qr_label.image = qr_photo  # Keep a reference
            qr_label.pack(pady=20)
            
            # Display connection details
            details_frame = tk.Frame(self.qr_display_frame, bg="white")
            details_frame.pack(pady=10)
            
            tk.Label(details_frame, text="📶 WiFi Connection Details", 
                    font=("Arial", 14, "bold"), bg="white", fg=self.colors['navy']).pack()
            
            tk.Label(details_frame, text=f"Network: {wifi_data['ssid']}", 
                    font=("Arial", 12), bg="white").pack()
            tk.Label(details_frame, text=f"Password: {wifi_data['wifi_password']}", 
                    font=("Arial", 12, "bold"), bg="white", fg=self.colors['success']).pack()
            tk.Label(details_frame, text=f"Access Code: {wifi_data['access_code']}", 
                    font=("Arial", 10), bg="white").pack()
            tk.Label(details_frame, text=f"Valid Until: {wifi_data['expiry_date']}", 
                    font=("Arial", 10), bg="white", fg=self.colors['warning']).pack()
            
            # Store for later use
            self.current_wifi_data = wifi_data
            
            messagebox.showinfo("Success", 
                               f"WiFi QR Code generated successfully!\n\n"
                               f"Network: {wifi_data['ssid']}\n"
                               f"Password: {wifi_data['wifi_password']}\n"
                               f"Valid until: {wifi_data['expiry_date']}\n\n"
                               f"You've earned 5 loyalty points!")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate WiFi QR code: {str(e)}")
    
    def email_qr_code(self):
        """Email QR code to customer"""
        if hasattr(self, 'current_wifi_data'):
            messagebox.showinfo("Email Sent", 
                               "WiFi QR code and instructions have been sent to your email address!")
        else:
            messagebox.showwarning("No QR Code", "Please generate a QR code first!")
    
    def sms_instructions(self):
        """Send SMS instructions"""
        if hasattr(self, 'current_wifi_data'):
            messagebox.showinfo("SMS Sent", 
                               "WiFi connection instructions have been sent to your phone!")
        else:
            messagebox.showwarning("No QR Code", "Please generate a QR code first!")
    
    def print_qr_code(self):
        """Print QR code"""
        if hasattr(self, 'current_wifi_data'):
            messagebox.showinfo("Print", 
                               "QR code has been sent to the printer at reception!")
        else:
            messagebox.showwarning("No QR Code", "Please generate a QR code first!")

if __name__ == "__main__":
    root = tk.Tk()
    app = WiFiQRSystem(root, customer_id=1, booking_id=1)
    root.mainloop()
