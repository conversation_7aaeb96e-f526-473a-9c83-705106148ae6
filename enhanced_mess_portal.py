import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import datetime
import random
from enhanced_db import Database
import threading
import time

class EnhancedMessPortal:
    def __init__(self, root, user_id=None, member_type="Honorary"):
        self.root = root
        self.user_id = user_id
        self.member_type = member_type
        self.db = Database('hq_logistics.db')
        
        self.root.title("🏛️ HUB MESS MEMBER PORTAL - Your Elite Community Hub")
        self.root.geometry("1400x900")
        self.root.configure(bg="#0f172a")  # Dark navy background
        
        # Modern color scheme
        self.colors = {
            'primary': '#1e3a8a',      # Navy blue
            'secondary': '#3b82f6',    # Light blue
            'accent': '#10b981',       # Green
            'warning': '#f59e0b',      # Orange
            'danger': '#ef4444',       # Red
            'dark': '#0f172a',         # Dark navy
            'light': '#f8fafc',        # Light gray
            'white': '#ffffff',
            'gold': '#fbbf24',         # Gold for premium
            'purple': '#8b5cf6'        # Purple for special
        }
        
        # Initialize chat and notification systems
        self.chat_messages = []
        self.active_threads = []
        self.notifications = []
        
        self.setup_ui()
        self.start_live_updates()
        self.show_welcome_popup()
        
    def setup_ui(self):
        """Setup the enhanced mess portal UI"""
        # Header with animated welcome
        self.create_header()
        
        # Main content area with tabs
        self.create_main_content()
        
        # Live notification bar
        self.create_notification_bar()
        
        # Floating action buttons
        self.create_floating_buttons()
        
    def create_header(self):
        """Create animated header with member info"""
        header_frame = tk.Frame(self.root, bg=self.colors['primary'], height=120)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # Logo and title section
        title_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        title_frame.pack(side=tk.LEFT, padx=20, pady=10)
        
        tk.Label(title_frame, text="🏛️ HUB MESS", 
                font=("Arial", 28, "bold"), fg=self.colors['gold'], 
                bg=self.colors['primary']).pack(anchor=tk.W)
        
        tk.Label(title_frame, text="Elite Member Community Portal", 
                font=("Arial", 14), fg=self.colors['white'], 
                bg=self.colors['primary']).pack(anchor=tk.W)
        
        # Member info section
        member_frame = tk.Frame(header_frame, bg=self.colors['secondary'], relief=tk.RAISED, bd=2)
        member_frame.pack(side=tk.RIGHT, padx=20, pady=15, fill=tk.Y)
        
        tk.Label(member_frame, text=f"👤 {self.member_type} Member", 
                font=("Arial", 12, "bold"), fg=self.colors['white'], 
                bg=self.colors['secondary']).pack(padx=15, pady=5)
        
        # Live stats
        self.stats_label = tk.Label(member_frame, text="🔥 Online: 24 | 💬 New: 5", 
                                   font=("Arial", 10), fg=self.colors['white'], 
                                   bg=self.colors['secondary'])
        self.stats_label.pack(padx=15, pady=2)
        
    def create_main_content(self):
        """Create main content with exciting tabs"""
        # Create notebook with custom styling
        style = ttk.Style()
        style.configure('Custom.TNotebook.Tab', padding=[20, 10])
        
        self.notebook = ttk.Notebook(self.root, style='Custom.TNotebook')
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create exciting tabs
        self.create_community_chat_tab()
        self.create_announcements_tab()
        self.create_hub_showcase_tab()
        self.create_events_tab()
        self.create_member_lounge_tab()
        self.create_rewards_tab()
        
    def create_community_chat_tab(self):
        """Create lively community chat area"""
        chat_frame = ttk.Frame(self.notebook)
        self.notebook.add(chat_frame, text="💬 Community Chat")
        
        # Chat header with online members
        chat_header = tk.Frame(chat_frame, bg=self.colors['accent'], height=60)
        chat_header.pack(fill=tk.X)
        chat_header.pack_propagate(False)
        
        tk.Label(chat_header, text="🌟 Live Community Chat - Share, Connect, Engage!", 
                font=("Arial", 16, "bold"), fg=self.colors['white'], 
                bg=self.colors['accent']).pack(pady=15)
        
        # Main chat area
        chat_main = tk.Frame(chat_frame)
        chat_main.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left side - Chat messages
        chat_left = tk.Frame(chat_main)
        chat_left.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Chat display area
        self.chat_display = scrolledtext.ScrolledText(
            chat_left, wrap=tk.WORD, height=20, font=("Arial", 11),
            bg=self.colors['light'], fg=self.colors['dark']
        )
        self.chat_display.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Message input area
        input_frame = tk.Frame(chat_left)
        input_frame.pack(fill=tk.X)
        
        self.message_entry = tk.Entry(input_frame, font=("Arial", 12), 
                                     bg=self.colors['white'])
        self.message_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        self.message_entry.bind('<Return>', self.send_message)
        
        tk.Button(input_frame, text="📤 Send", command=self.send_message,
                 bg=self.colors['accent'], fg=self.colors['white'], 
                 font=("Arial", 10, "bold")).pack(side=tk.RIGHT)
        
        # Right side - Active threads and online members
        chat_right = tk.Frame(chat_main, width=300)
        chat_right.pack(side=tk.RIGHT, fill=tk.Y)
        chat_right.pack_propagate(False)
        
        # Active threads section
        threads_frame = ttk.LabelFrame(chat_right, text="🔥 Hot Threads", padding=10)
        threads_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.threads_listbox = tk.Listbox(threads_frame, height=8, 
                                         bg=self.colors['light'], font=("Arial", 10))
        self.threads_listbox.pack(fill=tk.X)
        self.threads_listbox.bind('<Double-1>', self.open_thread)
        
        # Create thread button
        tk.Button(threads_frame, text="➕ Create New Thread", 
                 command=self.create_new_thread, bg=self.colors['purple'], 
                 fg=self.colors['white'], font=("Arial", 10, "bold")).pack(fill=tk.X, pady=(5, 0))
        
        # Online members section
        members_frame = ttk.LabelFrame(chat_right, text="👥 Online Members", padding=10)
        members_frame.pack(fill=tk.BOTH, expand=True)
        
        self.members_listbox = tk.Listbox(members_frame, bg=self.colors['light'], 
                                         font=("Arial", 10))
        self.members_listbox.pack(fill=tk.BOTH, expand=True)
        
        # Load initial data
        self.load_chat_messages()
        self.load_active_threads()
        self.load_online_members()
        
    def create_announcements_tab(self):
        """Create exciting announcements tab"""
        announce_frame = ttk.Frame(self.notebook)
        self.notebook.add(announce_frame, text="📢 Announcements")
        
        # Header
        header = tk.Frame(announce_frame, bg=self.colors['warning'], height=80)
        header.pack(fill=tk.X)
        header.pack_propagate(False)
        
        tk.Label(header, text="📢 Official Announcements & Updates", 
                font=("Arial", 18, "bold"), fg=self.colors['white'], 
                bg=self.colors['warning']).pack(pady=20)
        
        # Announcements content
        content_frame = tk.Frame(announce_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Create announcement cards
        announcements = [
            {
                "title": "🎉 New Year Gala Dinner",
                "content": "Join us for an exclusive New Year celebration at the Hub Mess! Premium menu, live entertainment, and special guest appearances.",
                "date": "Dec 31, 2024",
                "priority": "high",
                "icon": "🎊"
            },
            {
                "title": "🏋️ Gym Equipment Upgrade",
                "content": "Brand new state-of-the-art fitness equipment has been installed! Free personal training sessions for all members this month.",
                "date": "Jan 15, 2024",
                "priority": "medium",
                "icon": "💪"
            },
            {
                "title": "🌐 WiFi Speed Upgrade",
                "content": "Internet speed upgraded to 500 Mbps! Enjoy lightning-fast connectivity throughout the premises.",
                "date": "Jan 10, 2024",
                "priority": "low",
                "icon": "⚡"
            }
        ]
        
        for i, announcement in enumerate(announcements):
            self.create_announcement_card(content_frame, announcement, i)
            
    def create_hub_showcase_tab(self):
        """Create exciting Hub Mess and Guest House showcase"""
        showcase_frame = ttk.Frame(self.notebook)
        self.notebook.add(showcase_frame, text="🏨 Hub Showcase")
        
        # Scrollable content
        canvas = tk.Canvas(showcase_frame, bg=self.colors['light'])
        scrollbar = ttk.Scrollbar(showcase_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.colors['light'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Hero section
        hero_frame = tk.Frame(scrollable_frame, bg=self.colors['primary'], height=200)
        hero_frame.pack(fill=tk.X, padx=20, pady=20)
        hero_frame.pack_propagate(False)
        
        tk.Label(hero_frame, text="🏛️ WELCOME TO HUB EXCELLENCE", 
                font=("Arial", 24, "bold"), fg=self.colors['gold'], 
                bg=self.colors['primary']).pack(pady=30)
        
        tk.Label(hero_frame, text="Where Luxury Meets Military Precision", 
                font=("Arial", 16), fg=self.colors['white'], 
                bg=self.colors['primary']).pack()
        
        # Features showcase
        features_frame = tk.Frame(scrollable_frame, bg=self.colors['light'])
        features_frame.pack(fill=tk.X, padx=20, pady=20)
        
        self.create_feature_showcase(features_frame)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def create_events_tab(self):
        """Create events and activities tab"""
        events_frame = ttk.Frame(self.notebook)
        self.notebook.add(events_frame, text="🎪 Events & Activities")
        
        # Events content will be added here
        tk.Label(events_frame, text="🎪 Exciting Events Coming Soon!", 
                font=("Arial", 20, "bold")).pack(pady=50)
        
    def create_member_lounge_tab(self):
        """Create exclusive member lounge"""
        lounge_frame = ttk.Frame(self.notebook)
        self.notebook.add(lounge_frame, text="👑 Member Lounge")
        
        # Lounge content will be added here
        tk.Label(lounge_frame, text="👑 Exclusive Member Benefits", 
                font=("Arial", 20, "bold")).pack(pady=50)
        
    def create_rewards_tab(self):
        """Create rewards and loyalty program"""
        rewards_frame = ttk.Frame(self.notebook)
        self.notebook.add(rewards_frame, text="🎁 Rewards")
        
        # Rewards content will be added here
        tk.Label(rewards_frame, text="🎁 Loyalty Rewards Program", 
                font=("Arial", 20, "bold")).pack(pady=50)
        
    def create_notification_bar(self):
        """Create live notification bar"""
        self.notification_frame = tk.Frame(self.root, bg=self.colors['accent'], height=40)
        self.notification_frame.pack(fill=tk.X, side=tk.BOTTOM)
        self.notification_frame.pack_propagate(False)
        
        self.notification_label = tk.Label(
            self.notification_frame, 
            text="🔔 Welcome to Hub Mess Portal! Check out our latest updates...", 
            font=("Arial", 11), fg=self.colors['white'], bg=self.colors['accent']
        )
        self.notification_label.pack(pady=8)
        
    def create_floating_buttons(self):
        """Create floating action buttons"""
        # Quick action button
        self.quick_action_btn = tk.Button(
            self.root, text="⚡", font=("Arial", 20), 
            bg=self.colors['danger'], fg=self.colors['white'],
            command=self.show_quick_actions, width=3, height=1
        )
        self.quick_action_btn.place(relx=0.95, rely=0.85, anchor=tk.CENTER)
        
    def send_message(self, event=None):
        """Send chat message"""
        message = self.message_entry.get().strip()
        if message:
            timestamp = datetime.datetime.now().strftime("%H:%M")
            formatted_message = f"[{timestamp}] You: {message}\n"
            
            self.chat_display.insert(tk.END, formatted_message)
            self.chat_display.see(tk.END)
            self.message_entry.delete(0, tk.END)
            
            # Simulate response (in real app, this would be from other users)
            self.root.after(2000, self.simulate_response)
            
    def simulate_response(self):
        """Simulate other users' responses"""
        responses = [
            "Great point! 👍",
            "I totally agree with that!",
            "Thanks for sharing! 🙏",
            "Interesting perspective 🤔",
            "Looking forward to it! 🎉"
        ]
        
        users = ["Cdr. Johnson", "Lt. Smith", "Maj. Williams", "Cpt. Brown"]
        
        timestamp = datetime.datetime.now().strftime("%H:%M")
        user = random.choice(users)
        response = random.choice(responses)
        
        formatted_response = f"[{timestamp}] {user}: {response}\n"
        self.chat_display.insert(tk.END, formatted_response)
        self.chat_display.see(tk.END)
        
    def load_chat_messages(self):
        """Load initial chat messages"""
        initial_messages = [
            "[14:30] Cdr. Johnson: Welcome everyone to the new portal! 🎉",
            "[14:32] Lt. Smith: This looks amazing! Love the new features 😍",
            "[14:35] Maj. Williams: Can't wait to try the new restaurant menu!",
            "[14:38] Cpt. Brown: The WiFi upgrade is fantastic! ⚡",
        ]
        
        for message in initial_messages:
            self.chat_display.insert(tk.END, message + "\n")
            
    def load_active_threads(self):
        """Load active discussion threads"""
        threads = [
            "🍽️ New Menu Suggestions",
            "🏋️ Gym Schedule Coordination", 
            "🎪 Event Planning Committee",
            "🌐 Tech Support & WiFi",
            "🏨 Guest House Reviews"
        ]
        
        for thread in threads:
            self.threads_listbox.insert(tk.END, thread)
            
    def load_online_members(self):
        """Load online members list"""
        members = [
            "👤 Cdr. Johnson (Online)",
            "👤 Lt. Smith (Away)",
            "👤 Maj. Williams (Online)",
            "👤 Cpt. Brown (Busy)",
            "👤 Lt.Cdr. Davis (Online)"
        ]
        
        for member in members:
            self.members_listbox.insert(tk.END, member)
            
    def create_new_thread(self):
        """Create new discussion thread"""
        thread_window = tk.Toplevel(self.root)
        thread_window.title("Create New Thread")
        thread_window.geometry("400x300")
        thread_window.configure(bg=self.colors['light'])
        
        tk.Label(thread_window, text="Create New Discussion Thread", 
                font=("Arial", 16, "bold")).pack(pady=20)
        
        tk.Label(thread_window, text="Thread Title:").pack(anchor=tk.W, padx=20)
        title_entry = tk.Entry(thread_window, width=50)
        title_entry.pack(padx=20, pady=5)
        
        tk.Label(thread_window, text="Description:").pack(anchor=tk.W, padx=20, pady=(10, 0))
        desc_text = tk.Text(thread_window, height=8, width=50)
        desc_text.pack(padx=20, pady=5)
        
        tk.Button(thread_window, text="🚀 Create Thread", 
                 command=lambda: self.create_thread(title_entry.get(), desc_text.get("1.0", tk.END), thread_window),
                 bg=self.colors['accent'], fg=self.colors['white']).pack(pady=20)
                 
    def create_thread(self, title, description, window):
        """Create and add new thread"""
        if title.strip():
            self.threads_listbox.insert(tk.END, f"🆕 {title.strip()}")
            window.destroy()
            messagebox.showinfo("Success", "Thread created successfully! 🎉")
            
    def open_thread(self, event):
        """Open selected thread"""
        selection = self.threads_listbox.curselection()
        if selection:
            thread_name = self.threads_listbox.get(selection[0])
            messagebox.showinfo("Thread", f"Opening thread: {thread_name}")
            
    def create_announcement_card(self, parent, announcement, index):
        """Create announcement card"""
        card_frame = tk.Frame(parent, bg=self.colors['white'], relief=tk.RAISED, bd=2)
        card_frame.pack(fill=tk.X, pady=10)
        
        # Priority color
        priority_colors = {
            'high': self.colors['danger'],
            'medium': self.colors['warning'], 
            'low': self.colors['accent']
        }
        
        # Header
        header_frame = tk.Frame(card_frame, bg=priority_colors[announcement['priority']], height=40)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text=f"{announcement['icon']} {announcement['title']}", 
                font=("Arial", 14, "bold"), fg=self.colors['white'], 
                bg=priority_colors[announcement['priority']]).pack(side=tk.LEFT, padx=15, pady=8)
        
        tk.Label(header_frame, text=announcement['date'], 
                font=("Arial", 10), fg=self.colors['white'], 
                bg=priority_colors[announcement['priority']]).pack(side=tk.RIGHT, padx=15, pady=8)
        
        # Content
        tk.Label(card_frame, text=announcement['content'], 
                font=("Arial", 11), wraplength=800, justify=tk.LEFT).pack(padx=15, pady=15, anchor=tk.W)
                
    def create_feature_showcase(self, parent):
        """Create feature showcase cards"""
        features = [
            {
                "title": "🏨 Premium Guest House",
                "description": "6 luxury rooms with AC, WiFi, and premium amenities",
                "highlight": "Special rates for members!"
            },
            {
                "title": "🍽️ Hub Mess Restaurant", 
                "description": "Authentic Nigerian cuisine and continental dishes",
                "highlight": "New menu items weekly!"
            },
            {
                "title": "🍺 Elite Mess Bar",
                "description": "Premium beverages in a relaxed atmosphere",
                "highlight": "Happy hour for members!"
            },
            {
                "title": "🏋️ Modern Fitness Center",
                "description": "State-of-the-art equipment and personal training",
                "highlight": "Free for all members!"
            }
        ]
        
        for i, feature in enumerate(features):
            row = i // 2
            col = i % 2
            
            feature_frame = tk.Frame(parent, bg=self.colors['white'], relief=tk.RAISED, bd=2)
            feature_frame.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")
            
            tk.Label(feature_frame, text=feature['title'], 
                    font=("Arial", 16, "bold"), fg=self.colors['primary']).pack(pady=10)
            
            tk.Label(feature_frame, text=feature['description'], 
                    font=("Arial", 11), wraplength=300).pack(padx=15)
            
            tk.Label(feature_frame, text=feature['highlight'], 
                    font=("Arial", 10, "bold"), fg=self.colors['accent']).pack(pady=10)
                    
        # Configure grid weights
        parent.grid_columnconfigure(0, weight=1)
        parent.grid_columnconfigure(1, weight=1)
        
    def show_welcome_popup(self):
        """Show welcome popup with exciting features"""
        popup = tk.Toplevel(self.root)
        popup.title("🎉 Welcome to Hub Mess Portal!")
        popup.geometry("500x400")
        popup.configure(bg=self.colors['primary'])
        popup.transient(self.root)
        popup.grab_set()
        
        # Center the popup
        popup.update_idletasks()
        x = (popup.winfo_screenwidth() // 2) - (popup.winfo_width() // 2)
        y = (popup.winfo_screenheight() // 2) - (popup.winfo_height() // 2)
        popup.geometry(f"+{x}+{y}")
        
        tk.Label(popup, text="🎉 WELCOME TO HUB MESS!", 
                font=("Arial", 20, "bold"), fg=self.colors['gold'], 
                bg=self.colors['primary']).pack(pady=20)
        
        welcome_text = """🌟 Your Elite Community Portal is Ready!

✨ New Features Available:
🗣️ Live Community Chat
📢 Real-time Announcements  
🎪 Exciting Events Calendar
🏨 Premium Guest House Booking
🍽️ Restaurant & Bar Orders
🎁 Loyalty Rewards Program

🚀 Start exploring and connecting with fellow members!"""
        
        tk.Label(popup, text=welcome_text, font=("Arial", 12), 
                fg=self.colors['white'], bg=self.colors['primary'], 
                justify=tk.LEFT).pack(padx=30, pady=20)
        
        tk.Button(popup, text="🚀 Let's Get Started!", 
                 command=popup.destroy, bg=self.colors['accent'], 
                 fg=self.colors['white'], font=("Arial", 12, "bold"),
                 padx=30, pady=10).pack(pady=20)
                 
    def show_quick_actions(self):
        """Show quick actions popup"""
        actions_popup = tk.Toplevel(self.root)
        actions_popup.title("⚡ Quick Actions")
        actions_popup.geometry("300x400")
        actions_popup.configure(bg=self.colors['light'])
        
        tk.Label(actions_popup, text="⚡ Quick Actions", 
                font=("Arial", 16, "bold")).pack(pady=15)
        
        actions = [
            ("🏨 Book Room", self.quick_book_room),
            ("🍽️ Order Food", self.quick_order_food),
            ("🍺 Bar Menu", self.quick_bar_menu),
            ("🏋️ Gym Schedule", self.quick_gym_schedule),
            ("💬 New Message", self.quick_new_message),
            ("📢 Announcements", self.quick_announcements)
        ]
        
        for action_text, action_command in actions:
            tk.Button(actions_popup, text=action_text, 
                     command=action_command, width=20, pady=5,
                     bg=self.colors['secondary'], fg=self.colors['white']).pack(pady=5)
                     
    def quick_book_room(self):
        messagebox.showinfo("Quick Book", "Opening room booking... 🏨")
        
    def quick_order_food(self):
        messagebox.showinfo("Quick Order", "Opening restaurant menu... 🍽️")
        
    def quick_bar_menu(self):
        messagebox.showinfo("Bar Menu", "Opening bar menu... 🍺")
        
    def quick_gym_schedule(self):
        messagebox.showinfo("Gym Schedule", "Opening gym schedule... 🏋️")
        
    def quick_new_message(self):
        self.notebook.select(0)  # Switch to chat tab
        self.message_entry.focus()
        
    def quick_announcements(self):
        self.notebook.select(1)  # Switch to announcements tab
        
    def start_live_updates(self):
        """Start live updates for notifications"""
        self.update_notifications()
        
    def update_notifications(self):
        """Update live notifications"""
        notifications = [
            "🔥 5 new messages in Community Chat",
            "🎉 New Year Gala tickets now available!",
            "🍽️ Today's special: Jollof Rice & Grilled Fish",
            "🏋️ Gym equipment maintenance completed",
            "🌐 WiFi speed upgraded to 500 Mbps!"
        ]
        
        current_notification = random.choice(notifications)
        self.notification_label.config(text=f"🔔 {current_notification}")
        
        # Update stats
        online_count = random.randint(20, 35)
        new_messages = random.randint(3, 8)
        self.stats_label.config(text=f"🔥 Online: {online_count} | 💬 New: {new_messages}")
        
        # Schedule next update
        self.root.after(5000, self.update_notifications)

if __name__ == "__main__":
    root = tk.Tk()
    app = EnhancedMessPortal(root, user_id=1, member_type="Honorary")
    root.mainloop()
