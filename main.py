import tkinter as tk
import sqlite3
import os
import enhanced_db as db
from login import <PERSON>gin<PERSON>pp
import honorary_mess_portal
import work_order

def initialize_database():
    """Initialize the database if it doesn't exist"""
    # Check if database file exists
    if not os.path.exists('hq_logistics.db'):
        # Create database and initialize tables
        database = db.EnhancedDB()

        # Initialize guest house rooms
        database.initialize_guest_house_rooms()

        print("Database initialized successfully")
    else:
        print("Database already exists")

def run_honorary_mess_portal():
    """Run the honorary mess portal directly for testing"""
    root = tk.Tk()
    honorary_mess_portal.HonoraryMessPortal(root)
    root.mainloop()

def run_work_order_system():
    """Run the work order system directly for testing"""
    root = tk.Tk()
    work_order.WorkOrderSystem(root)
    root.mainloop()

if __name__ == "__main__":
    # Initialize database
    initialize_database()

    # Start the application
    root = tk.Tk()
    LoginApp(root)
    root.mainloop()

    # To test individual modules, uncomment one of these:
    # run_honorary_mess_portal()
    # run_work_order_system()
