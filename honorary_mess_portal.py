import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import datetime
import os
import io
from PIL import Image, ImageTk
import enhanced_db as db
import ttkthemes
import ttkbootstrap as tbs
from ttkbootstrap.constants import *
import webbrowser

class HonoraryMessPortal:
    def __init__(self, root, user_id=None, role=None):
        self.root = root
        self.user_id = user_id
        self.role = role

        # Set window properties
        self.root.title("HQ LOC Honorary Mess Portal")
        self.root.geometry("1280x800")
        self.root.minsize(1024, 768)

        # Create a database connection
        self.conn = sqlite3.connect('hq_logistics.db')
        self.cur = self.conn.cursor()

        # Set theme
        self.style = tbs.Style(theme="superhero")

        # Create main container
        self.main_container = tbs.Frame(self.root)
        self.main_container.pack(fill=tk.BOTH, expand=True)

        # Create header, sidebar and content area
        self.create_layout()

        # Show home page by default
        self.show_home()

    def create_layout(self):
        """Create the main layout with header, sidebar and content area"""
        # Header with logo and title
        self.header = tbs.Frame(self.main_container, bootstyle="primary")
        self.header.pack(fill=tk.X, side=tk.TOP)

        # Try to load logo
        try:
            logo_img = Image.open("HQ LOC logo.jpg")
            logo_img = logo_img.resize((80, 80), Image.LANCZOS)
            logo_photo = ImageTk.PhotoImage(logo_img)
            logo_label = tbs.Label(self.header, image=logo_photo)
            logo_label.image = logo_photo
            logo_label.pack(side=tk.LEFT, padx=20, pady=10)
        except:
            # If logo not found, use text instead
            logo_label = tbs.Label(self.header, text="HQ LOC", font=("Arial", 24, "bold"),
                                  bootstyle="inverse-primary")
            logo_label.pack(side=tk.LEFT, padx=20, pady=10)

        # Title
        title_frame = tbs.Frame(self.header, bootstyle="primary")
        title_frame.pack(side=tk.LEFT, padx=20, pady=10)

        tbs.Label(title_frame, text="COMMAND FACILITIES MANAGER",
                 font=("Arial", 16, "bold"), bootstyle="inverse-primary").pack(anchor=tk.W)
        tbs.Label(title_frame, text="HONORARY MESS MEMBERS PORTAL",
                 font=("Arial", 14), bootstyle="inverse-primary").pack(anchor=tk.W)

        # User info and logout button (right side of header)
        user_frame = tbs.Frame(self.header, bootstyle="primary")
        user_frame.pack(side=tk.RIGHT, padx=20, pady=10)

        if self.user_id:
            # Get user details
            self.cur.execute("SELECT full_name, role FROM users WHERE id = ?", (self.user_id,))
            user = self.cur.fetchone()
            if user:
                user_name, user_role = user
                tbs.Label(user_frame, text=f"Welcome, {user_name}",
                         bootstyle="inverse-primary").pack(anchor=tk.E)
                tbs.Label(user_frame, text=f"Role: {user_role}",
                         bootstyle="inverse-primary").pack(anchor=tk.E)

        tbs.Button(user_frame, text="Logout", command=self.logout,
                  bootstyle="outline-light").pack(anchor=tk.E, pady=5)

        # Main content area with sidebar and content
        self.content_container = tbs.Frame(self.main_container)
        self.content_container.pack(fill=tk.BOTH, expand=True)

        # Sidebar
        self.sidebar = tbs.Frame(self.content_container, width=250, bootstyle="secondary")
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y)
        self.sidebar.pack_propagate(False)  # Prevent the sidebar from shrinking

        # Create navigation menu
        self.create_navigation_menu()

        # Content area
        self.content = tbs.Frame(self.content_container)
        self.content.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Create frames for different sections
        self.home_frame = tbs.Frame(self.content)
        self.about_frame = tbs.Frame(self.content)
        self.officials_frame = tbs.Frame(self.content)
        self.membership_frame = tbs.Frame(self.content)
        self.members_frame = tbs.Frame(self.content)
        self.events_frame = tbs.Frame(self.content)
        self.gallery_frame = tbs.Frame(self.content)
        self.contact_frame = tbs.Frame(self.content)

        # Footer
        self.footer = tbs.Frame(self.main_container, height=30, bootstyle="primary")
        self.footer.pack(fill=tk.X, side=tk.BOTTOM)
        tbs.Label(self.footer, text="© 2023 HQ Logistics Command - All Rights Reserved",
                 bootstyle="inverse-primary").pack(pady=5)

    def create_navigation_menu(self):
        """Create the navigation menu in the sidebar"""
        # Add some padding at the top
        tbs.Label(self.sidebar, text="", bootstyle="secondary").pack(pady=10)

        # Menu buttons
        menu_items = [
            ("Home", self.show_home, "house"),
            ("About the Mess", self.show_about, "info-circle"),
            ("Mess Officials", self.show_officials, "people"),
            ("Membership", self.show_membership, "card-list"),
            ("Members Directory", self.show_members, "person-lines"),
            ("Events", self.show_events, "calendar"),
            ("Photo Gallery", self.show_gallery, "images"),
            ("Contact & Support", self.show_contact, "envelope")
        ]

        for text, command, icon in menu_items:
            btn = tbs.Button(self.sidebar, text=f" {text}", command=command,
                            bootstyle="link-light", width=20)
            btn.pack(fill=tk.X, pady=5, padx=10)

    def show_frame(self, frame):
        """Show the specified frame and hide others"""
        for f in [self.home_frame, self.about_frame, self.officials_frame,
                 self.membership_frame, self.members_frame, self.events_frame,
                 self.gallery_frame, self.contact_frame]:
            f.pack_forget()

        frame.pack(fill=tk.BOTH, expand=True)

    def show_home(self):
        """Show the home page"""
        self.show_frame(self.home_frame)

        # Clear existing widgets
        for widget in self.home_frame.winfo_children():
            widget.destroy()

        # Create a scrollable frame
        canvas = tk.Canvas(self.home_frame)
        scrollbar = tbs.Scrollbar(self.home_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tbs.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Hero section
        hero_frame = tbs.Frame(scrollable_frame)
        hero_frame.pack(fill=tk.X, pady=0)

        # Try to load hero image
        try:
            hero_img = Image.open("mess_hall.jpg")
            hero_img = hero_img.resize((1000, 400), Image.LANCZOS)
            hero_photo = ImageTk.PhotoImage(hero_img)
            hero_label = tbs.Label(hero_frame, image=hero_photo)
            hero_label.image = hero_photo
            hero_label.pack(fill=tk.X)

            # Overlay text
            overlay_frame = tbs.Frame(hero_frame)
            overlay_frame.place(relx=0.5, rely=0.5, anchor=tk.CENTER)

            tbs.Label(overlay_frame, text="WELCOME TO THE",
                     font=("Arial", 20), background="#00000080", foreground="white").pack()
            tbs.Label(overlay_frame, text="COMMAND FACILITIES MESS",
                     font=("Arial", 32, "bold"), background="#00000080", foreground="white").pack()
            tbs.Label(overlay_frame, text="HONOR • TRADITION • EXCELLENCE",
                     font=("Arial", 16), background="#00000080", foreground="white").pack()

        except:
            # If image not found, use colored background
            hero_label = tbs.Label(hero_frame, text="WELCOME TO THE\nCOMMAND FACILITIES MESS\nHONOR • TRADITION • EXCELLENCE",
                                  font=("Arial", 24, "bold"), bootstyle="light", background="#3a86ff")
            hero_label.pack(fill=tk.X, ipady=100)

        # Welcome message
        welcome_frame = tbs.Frame(scrollable_frame, padding=20)
        welcome_frame.pack(fill=tk.X, pady=20)

        tbs.Label(welcome_frame, text="Welcome to the Honorary Mess Members Portal",
                 font=("Arial", 24, "bold")).pack(anchor=tk.W)

        tbs.Label(welcome_frame, text="The Headquarters Logistics Command Mess is a prestigious institution that upholds the finest traditions of the Nigerian Navy. Our Honorary Mess Members are distinguished individuals who contribute to and benefit from the rich heritage and camaraderie of our naval community.",
                 font=("Arial", 12), wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=10)

        tbs.Label(welcome_frame, text="This portal provides information about membership benefits, upcoming events, and serves as a resource for current and prospective honorary members.",
                 font=("Arial", 12), wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=10)

        # Featured sections
        featured_frame = tbs.Frame(scrollable_frame, padding=20)
        featured_frame.pack(fill=tk.X)

        # Configure grid
        for i in range(3):
            featured_frame.columnconfigure(i, weight=1)

        # Upcoming Events
        self.create_card(featured_frame, 0, 0, "Upcoming Events",
                        "View our calendar of upcoming mess events and activities.",
                        "calendar", self.show_events)

        # Membership Benefits
        self.create_card(featured_frame, 0, 1, "Membership Benefits",
                        "Learn about the exclusive benefits of honorary mess membership.",
                        "award", self.show_membership)

        # Photo Gallery
        self.create_card(featured_frame, 0, 2, "Photo Gallery",
                        "Browse photos from recent mess events and activities.",
                        "images", self.show_gallery)

        # News and announcements
        news_frame = tbs.LabelFrame(scrollable_frame, text="Latest News & Announcements",
                                   padding=20, bootstyle="primary")
        news_frame.pack(fill=tk.X, padx=20, pady=20)

        # Sample news items
        news_items = [
            {
                "title": "Annual Mess Dinner - December 15, 2023",
                "content": "The annual formal mess dinner will be held on December 15. All honorary members are invited to attend this prestigious event.",
                "date": "November 1, 2023"
            },
            {
                "title": "New Membership Applications Open",
                "content": "Applications for new honorary mess memberships are now being accepted. Deadline for submission is January 31, 2024.",
                "date": "October 15, 2023"
            },
            {
                "title": "Mess Renovation Completed",
                "content": "The renovation of the main dining hall has been completed. Members are invited to visit and enjoy the enhanced facilities.",
                "date": "September 30, 2023"
            }
        ]

        for i, news in enumerate(news_items):
            news_item_frame = tbs.Frame(news_frame)
            news_item_frame.pack(fill=tk.X, pady=10)

            tbs.Label(news_item_frame, text=news["title"],
                     font=("Arial", 14, "bold")).pack(anchor=tk.W)
            tbs.Label(news_item_frame, text=news["content"],
                     wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=5)
            tbs.Label(news_item_frame, text=f"Posted on: {news['date']}",
                     font=("Arial", 10, "italic")).pack(anchor=tk.W)

            if i < len(news_items) - 1:
                tbs.Separator(news_frame, bootstyle="secondary").pack(fill=tk.X, pady=10)

    def create_card(self, parent, row, col, title, description, icon, command):
        """Create a card for the dashboard"""
        card = tbs.Frame(parent, bootstyle="light")
        card.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

        tbs.Label(card, text=title, font=("Arial", 16, "bold")).pack(pady=10)
        tbs.Label(card, text=description, wraplength=250, height=3).pack(pady=10, padx=10)
        tbs.Button(card, text=f"View {title}", command=command,
                  bootstyle="outline-primary").pack(pady=10)

    def show_about(self):
        """Show the about page"""
        self.show_frame(self.about_frame)

        # Clear existing widgets
        for widget in self.about_frame.winfo_children():
            widget.destroy()

        # Create a scrollable frame
        canvas = tk.Canvas(self.about_frame)
        scrollbar = tbs.Scrollbar(self.about_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tbs.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # About the Mess
        about_frame = tbs.Frame(scrollable_frame, padding=20)
        about_frame.pack(fill=tk.X)

        tbs.Label(about_frame, text="About the HQ Logistics Command Mess",
                 font=("Arial", 24, "bold")).pack(anchor=tk.W)

        # History section
        history_frame = tbs.LabelFrame(scrollable_frame, text="History", padding=20)
        history_frame.pack(fill=tk.X, padx=20, pady=10)

        tbs.Label(history_frame, text="The Headquarters Logistics Command Mess was established in [year] to serve as the social and ceremonial center for officers of the Nigerian Navy Logistics Command. Over the years, it has evolved into a prestigious institution that upholds naval traditions while fostering camaraderie among officers and honorary members.",
                 wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=10)

        tbs.Label(history_frame, text="The mess has hosted numerous dignitaries, including heads of state, military leaders, and distinguished civilians. Its rich history is reflected in the memorabilia and artifacts displayed throughout the facility.",
                 wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=10)

        # Mission and Values
        mission_frame = tbs.LabelFrame(scrollable_frame, text="Mission and Values", padding=20)
        mission_frame.pack(fill=tk.X, padx=20, pady=10)

        tbs.Label(mission_frame, text="Mission",
                 font=("Arial", 14, "bold")).pack(anchor=tk.W)
        tbs.Label(mission_frame, text="To provide a distinguished social environment that upholds naval traditions, fosters camaraderie, and promotes professional development among officers and honorary members.",
                 wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=10)

        tbs.Label(mission_frame, text="Values",
                 font=("Arial", 14, "bold")).pack(anchor=tk.W)

        values = ["Honor: Upholding the highest standards of integrity and ethical conduct",
                 "Tradition: Preserving and celebrating naval customs and heritage",
                 "Excellence: Striving for the highest quality in all aspects of mess operations",
                 "Camaraderie: Fostering friendship and mutual support among members",
                 "Service: Dedicated to serving members and the broader naval community"]

        for value in values:
            tbs.Label(mission_frame, text=f"• {value}",
                     wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=2)

        # Facilities
        facilities_frame = tbs.LabelFrame(scrollable_frame, text="Facilities", padding=20)
        facilities_frame.pack(fill=tk.X, padx=20, pady=10)

        tbs.Label(facilities_frame, text="The Command Facilities Mess boasts a range of world-class facilities:",
                 wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=10)

        facilities = ["Formal Dining Hall: Elegant space for formal dinners and special events",
                     "Lounge: Comfortable area for relaxation and socializing",
                     "Bar: Well-stocked bar offering a selection of beverages",
                     "Conference Room: Equipped for meetings and presentations",
                     "Recreation Area: Including games and entertainment options",
                     "Guest Accommodations: Comfortable rooms for overnight stays"]

        for facility in facilities:
            tbs.Label(facilities_frame, text=f"• {facility}",
                     wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=2)

    def show_officials(self):
        """Show the mess officials page"""
        self.show_frame(self.officials_frame)

        # Clear existing widgets
        for widget in self.officials_frame.winfo_children():
            widget.destroy()

        # Create a scrollable frame
        canvas = tk.Canvas(self.officials_frame)
        scrollbar = tbs.Scrollbar(self.officials_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tbs.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Officials header
        officials_header = tbs.Frame(scrollable_frame, padding=20)
        officials_header.pack(fill=tk.X)

        tbs.Label(officials_header, text="Mess Officials",
                 font=("Arial", 24, "bold")).pack(anchor=tk.W)

        tbs.Label(officials_header, text="The HQ Logistics Command Mess is led by a dedicated team of officers who ensure the highest standards are maintained in all aspects of mess operations.",
                 wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=10)

        # Officials grid
        officials_grid = tbs.Frame(scrollable_frame, padding=20)
        officials_grid.pack(fill=tk.X)

        # Configure grid
        for i in range(2):
            officials_grid.columnconfigure(i, weight=1)

        # Officials data
        officials = [
            {
                "title": "Patron",
                "name": "Rear Admiral OO Fadeyi",
                "position": "Flag Officer Commanding",
                "photo": "fadeyi.jpg",
                "contact": "<EMAIL>"
            },
            {
                "title": "President",
                "name": "Rear Admiral Yusuf",
                "position": "Mess Committee President",
                "photo": "yusuf.jpg",
                "contact": "<EMAIL>"
            },
            {
                "title": "Wine Member",
                "name": "Commander Udeh",
                "position": "Mess Wine Member",
                "photo": "udeh.jpg",
                "contact": "<EMAIL>"
            },
            {
                "title": "Property Member",
                "name": "Lieutenant Commander Ejugwu",
                "position": "Mess Property Member",
                "photo": "ejugwu.jpg",
                "contact": "<EMAIL>"
            }
        ]

        # Create official cards
        for i, official in enumerate(officials):
            row = i // 2
            col = i % 2
            self.create_official_card(officials_grid, row, col, official)

        # Contact information
        contact_frame = tbs.LabelFrame(scrollable_frame, text="Contact Information", padding=20)
        contact_frame.pack(fill=tk.X, padx=20, pady=20)

        tbs.Label(contact_frame, text="For inquiries related to mess operations, please contact the appropriate official:",
                 wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=10)

        tbs.Label(contact_frame, text="General Inquiries: <EMAIL>",
                 font=("Arial", 12)).pack(anchor=tk.W, pady=2)
        tbs.Label(contact_frame, text="Membership: <EMAIL>",
                 font=("Arial", 12)).pack(anchor=tk.W, pady=2)
        tbs.Label(contact_frame, text="Events: <EMAIL>",
                 font=("Arial", 12)).pack(anchor=tk.W, pady=2)
        tbs.Label(contact_frame, text="Phone: +234 (0) XXX-XXX-XXXX",
                 font=("Arial", 12)).pack(anchor=tk.W, pady=2)

    def create_official_card(self, parent, row, col, official):
        """Create a card for an official"""
        card = tbs.Frame(parent, bootstyle="light")
        card.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

        # Try to load official's photo
        try:
            img = Image.open(official["photo"])
            img = img.resize((150, 150), Image.LANCZOS)
            photo = ImageTk.PhotoImage(img)
            photo_label = tbs.Label(card, image=photo)
            photo_label.image = photo
            photo_label.pack(pady=10)
        except:
            # If photo not found, use placeholder
            photo_frame = tbs.Frame(card, width=150, height=150, bootstyle="secondary")
            photo_frame.pack(pady=10)
            photo_frame.pack_propagate(False)
            tbs.Label(photo_frame, text=official["name"][0],
                     font=("Arial", 48, "bold")).place(relx=0.5, rely=0.5, anchor=tk.CENTER)

        tbs.Label(card, text=official["title"],
                 font=("Arial", 16, "bold")).pack(pady=5)
        tbs.Label(card, text=official["name"],
                 font=("Arial", 14)).pack(pady=2)
        tbs.Label(card, text=official["position"],
                 font=("Arial", 12, "italic")).pack(pady=2)
        tbs.Label(card, text=official["contact"],
                 font=("Arial", 10)).pack(pady=5)

    def show_membership(self):
        """Show the membership page"""
        self.show_frame(self.membership_frame)

        # Clear existing widgets
        for widget in self.membership_frame.winfo_children():
            widget.destroy()

        # Create a scrollable frame
        canvas = tk.Canvas(self.membership_frame)
        scrollbar = tbs.Scrollbar(self.membership_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tbs.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Membership header
        header_frame = tbs.Frame(scrollable_frame, padding=20)
        header_frame.pack(fill=tk.X)

        tbs.Label(header_frame, text="Honorary Mess Membership",
                 font=("Arial", 24, "bold")).pack(anchor=tk.W)

        tbs.Label(header_frame, text="Becoming an Honorary Member of the HQ Logistics Command Mess is a privilege extended to distinguished individuals who have made significant contributions to the Nigerian Navy or society at large.",
                 wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=10)

        # Benefits section
        benefits_frame = tbs.LabelFrame(scrollable_frame, text="Membership Benefits", padding=20)
        benefits_frame.pack(fill=tk.X, padx=20, pady=10)

        benefits = [
            "Access to all mess facilities including dining, bar, and recreational areas",
            "Invitation to exclusive mess events and functions",
            "Eligibility to book guest accommodations at preferential rates",
            "Networking opportunities with naval officers and other distinguished members",
            "Participation in mess traditions and ceremonies",
            "Ability to host personal or professional events at the mess (subject to availability)",
            "Regular updates on mess activities and naval news"
        ]

        for benefit in benefits:
            benefit_frame = tbs.Frame(benefits_frame)
            benefit_frame.pack(fill=tk.X, pady=5)

            tbs.Label(benefit_frame, text="•", font=("Arial", 14, "bold"),
                     bootstyle="primary").pack(side=tk.LEFT, padx=(0, 10))
            tbs.Label(benefit_frame, text=benefit, wraplength=850,
                     justify=tk.LEFT).pack(side=tk.LEFT, fill=tk.X)

        # Apply button
        apply_frame = tbs.Frame(scrollable_frame, padding=20)
        apply_frame.pack(fill=tk.X, pady=20)

        tbs.Label(apply_frame, text="Ready to apply for Honorary Mess Membership?",
                 font=("Arial", 14, "bold")).pack(pady=10)

        tbs.Button(apply_frame, text="Apply for Membership",
                  bootstyle="success", command=self.show_application_form).pack(pady=10)

    def show_application_form(self):
        """Show the membership application form"""
        # This would open a membership application form
        messagebox.showinfo("Application Form", "Membership application form coming soon!")

    def show_members(self):
        """Show the members directory page"""
        self.show_frame(self.members_frame)

        # Clear existing widgets
        for widget in self.members_frame.winfo_children():
            widget.destroy()

        # Create a scrollable frame
        canvas = tk.Canvas(self.members_frame)
        scrollbar = tbs.Scrollbar(self.members_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tbs.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Members header
        header_frame = tbs.Frame(scrollable_frame, padding=20)
        header_frame.pack(fill=tk.X)

        tbs.Label(header_frame, text="Honorary Mess Members Directory",
                 font=("Arial", 24, "bold")).pack(anchor=tk.W)

        tbs.Label(header_frame, text="Browse our directory of distinguished Honorary Mess Members.",
                 wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=10)

        # Search and filter
        filter_frame = tbs.Frame(scrollable_frame, padding=10)
        filter_frame.pack(fill=tk.X, padx=20, pady=10)

        # Search field
        search_frame = tbs.Frame(filter_frame)
        search_frame.pack(side=tk.LEFT, padx=10)

        tbs.Label(search_frame, text="Search:").pack(side=tk.LEFT, padx=5)
        search_var = tk.StringVar()
        tbs.Entry(search_frame, textvariable=search_var, width=30).pack(side=tk.LEFT, padx=5)

        # Filter by year
        year_frame = tbs.Frame(filter_frame)
        year_frame.pack(side=tk.LEFT, padx=10)

        tbs.Label(year_frame, text="Join Year:").pack(side=tk.LEFT, padx=5)
        year_var = tk.StringVar()
        year_combo = tbs.Combobox(year_frame, textvariable=year_var, width=10)
        year_combo['values'] = ('All', '2023', '2022', '2021', '2020', '2019', 'Earlier')
        year_combo.current(0)
        year_combo.pack(side=tk.LEFT, padx=5)

        # Search button
        tbs.Button(filter_frame, text="Search",
                  command=lambda: self.filter_members(search_var.get(), year_var.get(), "All"),
                  bootstyle="primary").pack(side=tk.LEFT, padx=10)

        # Members list
        members_frame = tbs.Frame(scrollable_frame, padding=10)
        members_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Sample member data
        members = [
            {
                "name": "Dr. James Wilson",
                "number": "HM-2023-0001",
                "joined": "2023",
                "profession": "Academic",
                "position": "University Professor",
                "photo": None
            },
            {
                "name": "Mrs. Sarah Johnson",
                "number": "HM-2022-0005",
                "joined": "2022",
                "profession": "Government",
                "position": "Permanent Secretary",
                "photo": None
            },
            {
                "name": "Mr. Robert Chen",
                "number": "HM-2021-0003",
                "joined": "2021",
                "profession": "Business",
                "position": "CEO, Global Shipping Ltd",
                "photo": None
            },
            {
                "name": "Capt. Michael Brown (Rtd)",
                "number": "HM-2020-0007",
                "joined": "2020",
                "profession": "Military",
                "position": "Former Naval Officer",
                "photo": None
            }
        ]

        # Configure grid
        for i in range(2):
            members_frame.columnconfigure(i, weight=1)

        # Create member cards
        for i, member in enumerate(members):
            row = i // 2
            col = i % 2
            self.create_member_card(members_frame, row, col, member)

    def create_member_card(self, parent, row, col, member):
        """Create a card for a member"""
        card = tbs.Frame(parent, bootstyle="light")
        card.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

        # Left side - photo
        photo_frame = tbs.Frame(card, width=100, height=120)
        photo_frame.pack(side=tk.LEFT, padx=10, pady=10)
        photo_frame.pack_propagate(False)

        if member.get("photo"):
            try:
                img = Image.open(member["photo"])
                img = img.resize((100, 120), Image.LANCZOS)
                photo = ImageTk.PhotoImage(img)
                photo_label = tbs.Label(photo_frame, image=photo)
                photo_label.image = photo
                photo_label.pack(fill=tk.BOTH, expand=True)
            except:
                self.create_placeholder_photo(photo_frame, member["name"])
        else:
            self.create_placeholder_photo(photo_frame, member["name"])

        # Right side - details
        details_frame = tbs.Frame(card)
        details_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)

        tbs.Label(details_frame, text=member["name"],
                 font=("Arial", 14, "bold")).pack(anchor=tk.W)
        tbs.Label(details_frame, text=f"Member No: {member['number']}",
                 font=("Arial", 10)).pack(anchor=tk.W, pady=2)
        tbs.Label(details_frame, text=f"Joined: {member['joined']}",
                 font=("Arial", 10)).pack(anchor=tk.W, pady=2)
        tbs.Label(details_frame, text=f"{member['profession']} - {member['position']}",
                 font=("Arial", 10, "italic")).pack(anchor=tk.W, pady=2)

        # View profile button
        tbs.Button(details_frame, text="View Profile",
                  command=lambda: self.view_member_profile(member),
                  bootstyle="outline-primary", width=15).pack(anchor=tk.W, pady=5)

    def create_placeholder_photo(self, parent, name):
        """Create a placeholder for member photo"""
        placeholder = tbs.Frame(parent, bootstyle="secondary")
        placeholder.pack(fill=tk.BOTH, expand=True)

        # Get initials
        initials = "".join([n[0] for n in name.split() if n])[:2].upper()

        tbs.Label(placeholder, text=initials,
                 font=("Arial", 36, "bold"),
                 bootstyle="inverse-secondary").place(relx=0.5, rely=0.5, anchor=tk.CENTER)

    def view_member_profile(self, member):
        """View a member's profile"""
        messagebox.showinfo("Member Profile", f"Viewing profile for {member['name']}")

    def filter_members(self, search_term, year, profession):
        """Filter the members list"""
        messagebox.showinfo("Filter Members",
                           f"Filtering members with: Search={search_term}, Year={year}, Profession={profession}")
        # In a real app, this would reload the members list with filters applied

    def show_events(self):
        """Show the events page"""
        self.show_frame(self.events_frame)

        # Clear existing widgets
        for widget in self.events_frame.winfo_children():
            widget.destroy()

        # Create a scrollable frame
        canvas = tk.Canvas(self.events_frame)
        scrollbar = tbs.Scrollbar(self.events_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tbs.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Events header
        header_frame = tbs.Frame(scrollable_frame, padding=20)
        header_frame.pack(fill=tk.X)

        tbs.Label(header_frame, text="Mess Events",
                 font=("Arial", 24, "bold")).pack(anchor=tk.W)

        tbs.Label(header_frame, text="Stay updated on upcoming events and activities at the HQ Logistics Command Mess.",
                 wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=10)

        # Sample events
        events = [
            {
                "title": "Annual Mess Dinner",
                "date": "December 15, 2023",
                "description": "The annual formal mess dinner is a prestigious event."
            },
            {
                "title": "New Year's Eve Celebration",
                "date": "December 31, 2023",
                "description": "Ring in the New Year with fellow mess members."
            },
            {
                "title": "Naval History Lecture",
                "date": "January 20, 2024",
                "description": "Join us for an enlightening lecture on Nigerian Naval History."
            }
        ]

        # Display events
        events_frame = tbs.Frame(scrollable_frame, padding=10)
        events_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        for event in events:
            event_card = tbs.Frame(events_frame, bootstyle="light")
            event_card.pack(fill=tk.X, pady=10, padx=5)

            tbs.Label(event_card, text=event["title"],
                     font=("Arial", 16, "bold")).pack(anchor=tk.W, padx=10, pady=5)
            tbs.Label(event_card, text=f"Date: {event['date']}",
                     font=("Arial", 12)).pack(anchor=tk.W, padx=10, pady=2)
            tbs.Label(event_card, text=event["description"],
                     wraplength=800).pack(anchor=tk.W, padx=10, pady=5)

            tbs.Button(event_card, text="View Details",
                      bootstyle="outline-primary").pack(anchor=tk.W, padx=10, pady=10)

    def show_gallery(self):
        """Show the photo gallery page"""
        self.show_frame(self.gallery_frame)

        # Clear existing widgets
        for widget in self.gallery_frame.winfo_children():
            widget.destroy()

        # Create a scrollable frame
        canvas = tk.Canvas(self.gallery_frame)
        scrollbar = tbs.Scrollbar(self.gallery_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tbs.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Gallery header
        header_frame = tbs.Frame(scrollable_frame, padding=20)
        header_frame.pack(fill=tk.X)

        tbs.Label(header_frame, text="Photo Gallery",
                 font=("Arial", 24, "bold")).pack(anchor=tk.W)

        tbs.Label(header_frame, text="Browse photos from recent mess events and activities.",
                 wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=10)

        # Create photo grid
        self.create_photo_grid(scrollable_frame)

    def create_photo_grid(self, parent):
        """Create a grid of photos"""
        # Sample photo data
        photos_frame = tbs.Frame(parent, padding=10)
        photos_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Configure grid
        for i in range(3):
            photos_frame.columnconfigure(i, weight=1)

        # Create placeholder photos
        for row in range(3):
            for col in range(3):
                self.create_photo_item(photos_frame, row, col)

    def create_photo_item(self, parent, row, col):
        """Create a photo item in the grid"""
        # Photo frame
        photo_frame = tbs.Frame(parent, width=250, height=200)
        photo_frame.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")
        photo_frame.pack_propagate(False)

        # Placeholder
        placeholder = tbs.Frame(photo_frame, bootstyle="secondary")
        placeholder.pack(fill=tk.BOTH, expand=True)

        # Add placeholder text
        tbs.Label(placeholder, text="PHOTO",
                 font=("Arial", 24, "bold"),
                 bootstyle="inverse-secondary").place(relx=0.5, rely=0.5, anchor=tk.CENTER)

    def show_contact(self):
        """Show the contact page"""
        self.show_frame(self.contact_frame)

        # Clear existing widgets
        for widget in self.contact_frame.winfo_children():
            widget.destroy()

        # Create a scrollable frame
        canvas = tk.Canvas(self.contact_frame)
        scrollbar = tbs.Scrollbar(self.contact_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tbs.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Contact header
        header_frame = tbs.Frame(scrollable_frame, padding=20)
        header_frame.pack(fill=tk.X)

        tbs.Label(header_frame, text="Contact & Support",
                 font=("Arial", 24, "bold")).pack(anchor=tk.W)

        tbs.Label(header_frame, text="Get in touch with the HQ Logistics Command Mess.",
                 wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=10)

        # Contact information
        contact_frame = tbs.Frame(scrollable_frame, padding=20)
        contact_frame.pack(fill=tk.X, padx=20, pady=10)

        tbs.Label(contact_frame, text="Contact Information",
                 font=("Arial", 18, "bold")).pack(anchor=tk.W, pady=10)

        # Address
        address_frame = tbs.Frame(contact_frame)
        address_frame.pack(fill=tk.X, pady=10, anchor=tk.W)

        tbs.Label(address_frame, text="Address:",
                 font=("Arial", 12, "bold")).pack(side=tk.LEFT, padx=(0, 10))
        tbs.Label(address_frame, text="HQ Logistics Command Mess, Naval Base, Apapa, Lagos",
                 font=("Arial", 12)).pack(side=tk.LEFT)

        # Phone
        phone_frame = tbs.Frame(contact_frame)
        phone_frame.pack(fill=tk.X, pady=5, anchor=tk.W)

        tbs.Label(phone_frame, text="Phone:",
                 font=("Arial", 12, "bold")).pack(side=tk.LEFT, padx=(0, 10))
        tbs.Label(phone_frame, text="+234 (0) XXX-XXX-XXXX",
                 font=("Arial", 12)).pack(side=tk.LEFT)

        # Email
        email_frame = tbs.Frame(contact_frame)
        email_frame.pack(fill=tk.X, pady=5, anchor=tk.W)

        tbs.Label(email_frame, text="Email:",
                 font=("Arial", 12, "bold")).pack(side=tk.LEFT, padx=(0, 10))
        tbs.Label(email_frame, text="<EMAIL>",
                 font=("Arial", 12)).pack(side=tk.LEFT)

        # Contact form
        form_frame = tbs.LabelFrame(scrollable_frame, text="Send us a message", padding=20)
        form_frame.pack(fill=tk.X, padx=20, pady=20)

        # Name field
        name_frame = tbs.Frame(form_frame)
        name_frame.pack(fill=tk.X, pady=5)

        tbs.Label(name_frame, text="Name:").pack(side=tk.LEFT, padx=(0, 10))
        name_var = tk.StringVar()
        tbs.Entry(name_frame, textvariable=name_var, width=40).pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Email field
        email_field_frame = tbs.Frame(form_frame)
        email_field_frame.pack(fill=tk.X, pady=5)

        tbs.Label(email_field_frame, text="Email:").pack(side=tk.LEFT, padx=(0, 10))
        email_var = tk.StringVar()
        tbs.Entry(email_field_frame, textvariable=email_var, width=40).pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Subject field
        subject_frame = tbs.Frame(form_frame)
        subject_frame.pack(fill=tk.X, pady=5)

        tbs.Label(subject_frame, text="Subject:").pack(side=tk.LEFT, padx=(0, 10))
        subject_var = tk.StringVar()
        tbs.Entry(subject_frame, textvariable=subject_var, width=40).pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Message field
        message_frame = tbs.Frame(form_frame)
        message_frame.pack(fill=tk.X, pady=5)

        tbs.Label(message_frame, text="Message:").pack(anchor=tk.W, pady=5)
        message_text = tk.Text(message_frame, width=60, height=10)
        message_text.pack(fill=tk.X, pady=5)

        # Submit button
        tbs.Button(form_frame, text="Send Message",
                  command=self.send_contact_message,
                  bootstyle="success").pack(pady=10)

    def send_contact_message(self):
        """Send a contact message"""
        messagebox.showinfo("Contact", "Message sent successfully!")

    def fix_unique_constraint_error(self):
        """Fix the UNIQUE constraint error shown in the screenshot"""
        # This method would be called when registering a user with a duplicate username
        # It would check if the username already exists and suggest alternatives

        # Sample implementation
        def check_username_exists(username):
            self.cur.execute("SELECT COUNT(*) FROM users WHERE username = ?", (username,))
            count = self.cur.fetchone()[0]
            return count > 0

        def suggest_alternative_username(username):
            # Add a number to the end of the username
            i = 1
            while check_username_exists(f"{username}{i}"):
                i += 1
            return f"{username}{i}"

        # Example usage:
        # if check_username_exists(username):
        #     alternative = suggest_alternative_username(username)
        #     messagebox.showinfo("Username Taken",
        #                        f"The username '{username}' is already taken. Try '{alternative}' instead.")
        #     return alternative
        # Example of how to use this method in the registration process:
        # username = entry_username.get()
        # if check_username_exists(username):
        #     alternative = suggest_alternative_username(username)
        #     messagebox.showinfo("Username Taken",
        #                        f"The username '{username}' is already taken. Try '{alternative}' instead.")
        #     entry_username.delete(0, tk.END)
        #     entry_username.insert(0, alternative)
        # else:
        #     # Proceed with registration
        #     register_user(username, password, email, etc.)

        # This method fixes the error shown in the screenshot where registration fails
        # due to a UNIQUE constraint on the username field
        pass

    def logout(self):
        """Log out and return to login screen"""
        if messagebox.askyesno("Logout", "Are you sure you want to logout?"):
            self.root.destroy()
            # In a real app, you would launch the login screen here
            import login
            root = tk.Tk()
            login.LoginApp(root)
            root.mainloop()

if __name__ == "__main__":
    # This is just for testing the portal directly
    root = tk.Tk()
    app = HonoraryMessPortal(root)
    root.mainloop()
