# Command Facilities Manager

A comprehensive management system for Headquarters Logistics Command Oghara Barrack facilities including Mess, restaurants, gym, and guest house.

## Features

### Inventory Management
- Add, update, and remove items from inventory
- Track quantities and expiration dates
- Generate QR codes for items
- Search and filter inventory by category
- Export inventory data to CSV

### Restaurant Management
- Manage menu items for Hub Mess and Ola Saad restaurants
- Process food orders
- Generate invoices for purchases

### Gym Membership
- Track memberships
- Record check-in and check-out times
- Monitor usage statistics

### Guest House Management
- Manage room bookings
- Track occupancy
- Process payments

### Customer Management
- Maintain customer database
- Track purchase history

### Invoice Generation
- Generate invoices for all transactions
- Track payment status

## Requirements

- Python 3.6+
- Required packages:
  - tkinter (included with Python)
  - sqlite3 (included with Python)
  - qrcode
  - pillow (PIL)

## Installation

1. Clone or download this repository
2. Install required packages:
   ```
   pip install qrcode pillow
   ```
3. Run the application:
   ```
   python hq_logistics.py
   ```

## Database Structure

The application uses SQLite for data storage with the following tables:

- `inventory`: Stores inventory items with details like name, category, price, quantity, expiration date, etc.
- `customers`: Stores customer information
- `restaurant_menu`: Stores menu items for restaurants
- `restaurant_orders`: Stores restaurant orders
- `order_items`: Stores items in restaurant orders
- `gym_memberships`: Stores gym membership information
- `gym_attendance`: Tracks gym check-in and check-out times
- `guest_house`: Stores guest house room information
- `guest_house_bookings`: Stores guest house bookings
- `invoices`: Stores invoice information
- `invoice_items`: Stores items in invoices

## Usage

### Inventory Management

1. Navigate to the "Inventory" tab
2. Fill in the item details in the form
3. Click "Add Item" to add a new item
4. Select an item from the list to update or delete it
5. Use the search box to find specific items
6. Filter items by category using the dropdown
7. Generate QR codes for items by selecting an item and clicking "Generate QR"

### Restaurant Management

1. Navigate to the "Restaurants" tab
2. Use the "Menu Management" sub-tab to add or update menu items
3. Use the "Orders" sub-tab to process food orders

### Gym Membership

1. Navigate to the "Gym" tab
2. Register new memberships
3. Record check-in and check-out times
4. View membership statistics

### Guest House Management

1. Navigate to the "Guest House" tab
2. Manage room information
3. Process bookings
4. Track occupancy

### Customer Management

1. Navigate to the "Customers" tab
2. Add or update customer information
3. View customer purchase history

### Invoice Generation

1. Navigate to the "Invoices" tab
2. Generate invoices for transactions
3. Track payment status

## Development

### Project Structure

- `hq_logistics.py`: Main application file with UI
- `enhanced_db.py`: Database connection and operations
- `README.md`: Documentation

### Future Enhancements

- User authentication and authorization
- Barcode scanning functionality
- Mobile application integration
- Reporting and analytics
- Cloud backup and synchronization

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Original Parts Inventory System by [Original Author]
- Enhanced and expanded by [Your Name]
