import sqlite3
import os

# Check if database exists
if os.path.exists('hq_logistics.db'):
    print("Database found!")
    
    # Connect to database
    conn = sqlite3.connect('hq_logistics.db')
    cur = conn.cursor()
    
    # Check if users table exists
    cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
    if cur.fetchone():
        print("Users table exists!")
        
        # Get all users
        cur.execute("SELECT username, password, role, full_name FROM users")
        users = cur.fetchall()
        
        print(f"\nFound {len(users)} users:")
        print("-" * 60)
        for user in users:
            print(f"Username: {user[0]}")
            print(f"Password: {user[1]}")
            print(f"Role: {user[2]}")
            print(f"Full Name: {user[3]}")
            print("-" * 60)
    else:
        print("Users table does not exist!")
        
        # Create users table and add default users
        print("Creating users table...")
        cur.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                last_login TEXT,
                status TEXT DEFAULT 'Active'
            )
        """)
        
        # Add default users
        default_users = [
            ("admin", "admin123", "Chief Staff Officer", "CSO", "<EMAIL>", "08000000000"),
            ("wineofficer", "wine123", "Wine Officer", "Wine Officer", "<EMAIL>", "08000000001"),
            ("property", "property123", "Property Officer", "Property Officer", "<EMAIL>", "08000000002"),
            ("social", "social123", "Social Secretary", "Social Secretary", "<EMAIL>", "08000000003")
        ]
        
        for user in default_users:
            try:
                cur.execute("""
                    INSERT INTO users (username, password, full_name, role, email, phone)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, user)
                print(f"Added user: {user[0]}")
            except sqlite3.IntegrityError:
                print(f"User {user[0]} already exists")
        
        conn.commit()
        print("Users table created and populated!")
        
        # Show users again
        cur.execute("SELECT username, password, role, full_name FROM users")
        users = cur.fetchall()
        
        print(f"\nNow found {len(users)} users:")
        print("-" * 60)
        for user in users:
            print(f"Username: {user[0]}")
            print(f"Password: {user[1]}")
            print(f"Role: {user[2]}")
            print(f"Full Name: {user[3]}")
            print("-" * 60)
    
    conn.close()
else:
    print("Database not found! Please run main.py first to initialize the database.")
