import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import datetime
import os
from PIL import Image, ImageTk
import ttkbootstrap as tbs
from ttkbootstrap.constants import *

class WorkOrderSystem:
    def __init__(self, root, user_id=None, role=None):
        self.root = root
        self.user_id = user_id
        self.role = role

        # Set window properties
        self.root.title("Command Facilities Manager - Maintenance Work Order System")
        self.root.geometry("1200x700")
        self.root.minsize(1024, 768)

        # Create a database connection
        self.conn = sqlite3.connect('hq_logistics.db')
        self.cur = self.conn.cursor()

        # Set theme
        self.style = tbs.Style(theme="superhero")

        # Create main container
        self.main_container = tbs.Frame(self.root)
        self.main_container.pack(fill=tk.BOTH, expand=True)

        # Create header, sidebar and content area
        self.create_layout()

        # Show dashboard by default
        self.show_dashboard()

    def create_layout(self):
        """Create the main layout with header, sidebar and content area"""
        # Header with logo and title
        self.header = tbs.Frame(self.main_container, bootstyle="primary")
        self.header.pack(fill=tk.X, side=tk.TOP)

        # Try to load logo
        try:
            logo_img = Image.open("HQ LOC logo.jpg")
            logo_img = logo_img.resize((80, 80), Image.LANCZOS)
            logo_photo = ImageTk.PhotoImage(logo_img)
            logo_label = tbs.Label(self.header, image=logo_photo)
            logo_label.image = logo_photo
            logo_label.pack(side=tk.LEFT, padx=20, pady=10)
        except:
            # If logo not found, use text instead
            logo_label = tbs.Label(self.header, text="CFM", font=("Arial", 24, "bold"),
                                  bootstyle="inverse-primary")
            logo_label.pack(side=tk.LEFT, padx=20, pady=10)

        # Title
        title_frame = tbs.Frame(self.header, bootstyle="primary")
        title_frame.pack(side=tk.LEFT, padx=20, pady=10)

        tbs.Label(title_frame, text="MAINTENANCE WORK ORDER SYSTEM",
                 font=("Arial", 16, "bold"), bootstyle="inverse-primary").pack(anchor=tk.W)

        # User info and logout button (right side of header)
        user_frame = tbs.Frame(self.header, bootstyle="primary")
        user_frame.pack(side=tk.RIGHT, padx=20, pady=10)

        if self.user_id:
            # Get user details
            self.cur.execute("SELECT full_name, role FROM users WHERE id = ?", (self.user_id,))
            user = self.cur.fetchone()
            if user:
                user_name, user_role = user
                tbs.Label(user_frame, text=f"Welcome, {user_name}",
                         bootstyle="inverse-primary").pack(anchor=tk.E)
                tbs.Label(user_frame, text=f"Role: {user_role}",
                         bootstyle="inverse-primary").pack(anchor=tk.E)

        tbs.Button(user_frame, text="Logout", command=self.logout,
                  bootstyle="outline-light").pack(anchor=tk.E, pady=5)

        # Main content area with sidebar and content
        self.content_container = tbs.Frame(self.main_container)
        self.content_container.pack(fill=tk.BOTH, expand=True)

        # Sidebar
        self.sidebar = tbs.Frame(self.content_container, width=250, bootstyle="secondary")
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y)
        self.sidebar.pack_propagate(False)  # Prevent the sidebar from shrinking

        # Create navigation menu
        self.create_navigation_menu()

        # Content area
        self.content = tbs.Frame(self.content_container)
        self.content.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Create frames for different sections
        self.dashboard_frame = tbs.Frame(self.content)
        self.new_request_frame = tbs.Frame(self.content)
        self.my_requests_frame = tbs.Frame(self.content)
        self.all_requests_frame = tbs.Frame(self.content)
        self.reports_frame = tbs.Frame(self.content)

        # Footer
        self.footer = tbs.Frame(self.main_container, height=30, bootstyle="primary")
        self.footer.pack(fill=tk.X, side=tk.BOTTOM)
        tbs.Label(self.footer, text="© 2023 HQ Logistics Command - All Rights Reserved",
                 bootstyle="inverse-primary").pack(pady=5)

    def create_navigation_menu(self):
        """Create the navigation menu in the sidebar"""
        # Add some padding at the top
        tbs.Label(self.sidebar, text="", bootstyle="secondary").pack(pady=10)

        # Menu buttons
        menu_items = [
            ("Dashboard", self.show_dashboard, "house"),
            ("New Work Order", self.show_new_request, "plus-circle"),
            ("My Work Orders", self.show_my_requests, "list-check"),
            ("All Work Orders", self.show_all_requests, "clipboard-list"),
            ("Reports", self.show_reports, "chart-bar")
        ]

        for text, command, icon in menu_items:
            btn = tbs.Button(self.sidebar, text=f" {text}", command=command,
                            bootstyle="link-light", width=20)
            btn.pack(fill=tk.X, pady=5, padx=10)

    def show_frame(self, frame):
        """Show the specified frame and hide others"""
        for f in [self.dashboard_frame, self.new_request_frame, self.my_requests_frame,
                 self.all_requests_frame, self.reports_frame]:
            f.pack_forget()

        frame.pack(fill=tk.BOTH, expand=True)

    def show_dashboard(self):
        """Show the dashboard"""
        self.show_frame(self.dashboard_frame)

        # Clear existing widgets
        for widget in self.dashboard_frame.winfo_children():
            widget.destroy()

        # Create a scrollable frame
        canvas = tk.Canvas(self.dashboard_frame)
        scrollbar = tbs.Scrollbar(self.dashboard_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tbs.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Dashboard header
        header_frame = tbs.Frame(scrollable_frame, padding=20)
        header_frame.pack(fill=tk.X)

        tbs.Label(header_frame, text="Maintenance Work Order Dashboard",
                 font=("Arial", 24, "bold")).pack(anchor=tk.W)

        tbs.Label(header_frame, text="Overview of maintenance work orders and requests.",
                 wraplength=900, justify=tk.LEFT).pack(anchor=tk.W, pady=10)

        # Stats cards
        stats_frame = tbs.Frame(scrollable_frame, padding=10)
        stats_frame.pack(fill=tk.X, padx=20, pady=10)

        # Configure grid
        for i in range(4):
            stats_frame.columnconfigure(i, weight=1)

        # Sample stats
        stats = [
            {"title": "Total Work Orders", "value": "24", "color": "primary"},
            {"title": "Pending", "value": "8", "color": "warning"},
            {"title": "In Progress", "value": "10", "color": "info"},
            {"title": "Completed", "value": "6", "color": "success"}
        ]

        for i, stat in enumerate(stats):
            self.create_stat_card(stats_frame, i, stat)

        # Recent work orders
        recent_frame = tbs.LabelFrame(scrollable_frame, text="Recent Work Orders", padding=20)
        recent_frame.pack(fill=tk.X, padx=20, pady=10)

        # Create treeview for recent work orders
        columns = ("ID", "Date", "Location", "Type", "Description", "Status", "Priority")
        recent_tree = tbs.Treeview(recent_frame, columns=columns, show="headings", height=6)

        # Define column headings
        for col in columns:
            recent_tree.heading(col, text=col)
            # Set column widths
            if col == "ID":
                recent_tree.column(col, width=50)
            elif col in ["Date", "Status", "Priority"]:
                recent_tree.column(col, width=100)
            elif col == "Description":
                recent_tree.column(col, width=300)
            else:
                recent_tree.column(col, width=120)

        # Add scrollbar
        scrollbar = tbs.Scrollbar(recent_frame, orient=tk.VERTICAL, command=recent_tree.yview)
        recent_tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        recent_tree.pack(fill=tk.BOTH, expand=True)

        # Sample data
        recent_data = [
            (1, "2023-11-15", "Guest Room 101", "Plumbing", "Leaking faucet in bathroom", "In Progress", "Medium"),
            (2, "2023-11-14", "Kitchen", "Electrical", "Light fixture not working", "Pending", "High"),
            (3, "2023-11-12", "Bar", "Appliance", "Refrigerator not cooling properly", "Completed", "High"),
            (4, "2023-11-10", "Gym", "Equipment", "Treadmill making unusual noise", "In Progress", "Low"),
            (5, "2023-11-08", "Guest Room 103", "Furniture", "Broken chair leg", "Completed", "Medium")
        ]

        for item in recent_data:
            recent_tree.insert("", tk.END, values=item)

        # Quick actions
        actions_frame = tbs.Frame(scrollable_frame, padding=20)
        actions_frame.pack(fill=tk.X, pady=10)

        tbs.Label(actions_frame, text="Quick Actions",
                 font=("Arial", 16, "bold")).pack(anchor=tk.W, pady=10)

        buttons_frame = tbs.Frame(actions_frame)
        buttons_frame.pack(fill=tk.X, pady=10)

        tbs.Button(buttons_frame, text="New Work Order",
                  command=self.show_new_request,
                  bootstyle="success").pack(side=tk.LEFT, padx=10)

        tbs.Button(buttons_frame, text="View All Work Orders",
                  command=self.show_all_requests,
                  bootstyle="info").pack(side=tk.LEFT, padx=10)

        tbs.Button(buttons_frame, text="Generate Report",
                  command=self.show_reports,
                  bootstyle="warning").pack(side=tk.LEFT, padx=10)

    def create_stat_card(self, parent, col, stat):
        """Create a statistics card"""
        card = tbs.Frame(parent, bootstyle=stat["color"])
        card.grid(row=0, column=col, padx=10, pady=10, sticky="nsew")

        tbs.Label(card, text=stat["title"],
                 font=("Arial", 14),
                 bootstyle=f"inverse-{stat['color']}").pack(pady=10)

        tbs.Label(card, text=stat["value"],
                 font=("Arial", 24, "bold"),
                 bootstyle=f"inverse-{stat['color']}").pack(pady=10)

    def show_new_request(self):
        """Show the new work order request form"""
        self.show_frame(self.new_request_frame)

        # Clear existing widgets
        for widget in self.new_request_frame.winfo_children():
            widget.destroy()

        # Create form
        form_frame = tbs.Frame(self.new_request_frame, padding=20)
        form_frame.pack(fill=tk.BOTH, expand=True)

        tbs.Label(form_frame, text="New Maintenance Work Order",
                 font=("Arial", 24, "bold")).pack(anchor=tk.W, pady=10)

        tbs.Label(form_frame, text="Please fill out the form below to submit a new maintenance work order request.",
                 wraplength=900).pack(anchor=tk.W, pady=10)

        # Form fields
        fields_frame = tbs.Frame(form_frame, padding=10)
        fields_frame.pack(fill=tk.X, pady=20)

        # Location
        location_frame = tbs.Frame(fields_frame)
        location_frame.pack(fill=tk.X, pady=10)

        tbs.Label(location_frame, text="Location:").pack(side=tk.LEFT, padx=(0, 10))
        location_var = tk.StringVar()
        location_combo = tbs.Combobox(location_frame, textvariable=location_var, width=30)
        location_combo['values'] = ('Guest Room', 'Restaurant', 'Bar', 'Kitchen', 'Gym', 'Common Area', 'Other')
        location_combo.pack(side=tk.LEFT)

        # Room/Area
        room_frame = tbs.Frame(fields_frame)
        room_frame.pack(fill=tk.X, pady=10)

        tbs.Label(room_frame, text="Room/Area:").pack(side=tk.LEFT, padx=(0, 10))
        room_var = tk.StringVar()
        tbs.Entry(room_frame, textvariable=room_var, width=30).pack(side=tk.LEFT)

        # Issue type
        issue_frame = tbs.Frame(fields_frame)
        issue_frame.pack(fill=tk.X, pady=10)

        tbs.Label(issue_frame, text="Issue Type:").pack(side=tk.LEFT, padx=(0, 10))
        issue_var = tk.StringVar()
        issue_combo = tbs.Combobox(issue_frame, textvariable=issue_var, width=30)
        issue_combo['values'] = ('Plumbing', 'Electrical', 'HVAC', 'Furniture', 'Appliance', 'Structural', 'Cleaning', 'Other')
        issue_combo.pack(side=tk.LEFT)

        # Priority
        priority_frame = tbs.Frame(fields_frame)
        priority_frame.pack(fill=tk.X, pady=10)

        tbs.Label(priority_frame, text="Priority:").pack(side=tk.LEFT, padx=(0, 10))
        priority_var = tk.StringVar()
        priority_combo = tbs.Combobox(priority_frame, textvariable=priority_var, width=30)
        priority_combo['values'] = ('Low', 'Medium', 'High', 'Emergency')
        priority_combo.current(1)  # Default to Medium
        priority_combo.pack(side=tk.LEFT)

        # Description
        desc_frame = tbs.Frame(fields_frame)
        desc_frame.pack(fill=tk.X, pady=10)

        tbs.Label(desc_frame, text="Description:").pack(anchor=tk.W)
        desc_text = tk.Text(desc_frame, width=60, height=6)
        desc_text.pack(fill=tk.X, pady=5)

        # Submit button
        button_frame = tbs.Frame(fields_frame)
        button_frame.pack(fill=tk.X, pady=20)

        tbs.Button(button_frame, text="Submit Work Order",
                  command=lambda: self.submit_work_order(
                      location_var.get(),
                      room_var.get(),
                      issue_var.get(),
                      priority_var.get(),
                      desc_text.get("1.0", tk.END)
                  ),
                  bootstyle="success").pack(side=tk.LEFT, padx=10)

        tbs.Button(button_frame, text="Clear Form",
                  command=lambda: self.clear_form(
                      location_combo,
                      room_var,
                      issue_combo,
                      priority_combo,
                      desc_text
                  ),
                  bootstyle="secondary").pack(side=tk.LEFT, padx=10)

    def submit_work_order(self, location, room, issue_type, priority, description):
        """Submit a new work order"""
        # Validate inputs
        if not all([location, room, issue_type, priority, description.strip()]):
            messagebox.showerror("Validation Error", "Please fill in all fields")
            return

        # In a real app, this would save to the database
        messagebox.showinfo("Success", "Work order submitted successfully!")

        # Clear the form
        self.show_dashboard()

    def clear_form(self, location_combo, room_var, issue_combo, priority_combo, desc_text):
        """Clear the form fields"""
        location_combo.set('')
        room_var.set('')
        issue_combo.set('')
        priority_combo.current(1)  # Reset to Medium
        desc_text.delete("1.0", tk.END)

    def show_my_requests(self):
        """Show the user's work order requests"""
        self.show_frame(self.my_requests_frame)

        # Clear existing widgets
        for widget in self.my_requests_frame.winfo_children():
            widget.destroy()

        # Create content
        content_frame = tbs.Frame(self.my_requests_frame, padding=20)
        content_frame.pack(fill=tk.BOTH, expand=True)

        tbs.Label(content_frame, text="My Work Orders",
                 font=("Arial", 24, "bold")).pack(anchor=tk.W, pady=10)

        # Filter options
        filter_frame = tbs.Frame(content_frame)
        filter_frame.pack(fill=tk.X, pady=10)

        # Status filter
        tbs.Label(filter_frame, text="Status:").pack(side=tk.LEFT, padx=(0, 5))
        status_var = tk.StringVar()
        status_combo = tbs.Combobox(filter_frame, textvariable=status_var, width=15)
        status_combo['values'] = ('All', 'Pending', 'In Progress', 'Completed', 'Cancelled')
        status_combo.current(0)
        status_combo.pack(side=tk.LEFT, padx=(0, 20))

        # Date range
        tbs.Label(filter_frame, text="Date Range:").pack(side=tk.LEFT, padx=(0, 5))
        date_var = tk.StringVar()
        date_combo = tbs.Combobox(filter_frame, textvariable=date_var, width=15)
        date_combo['values'] = ('All Time', 'This Week', 'This Month', 'Last Month', 'Custom')
        date_combo.current(0)
        date_combo.pack(side=tk.LEFT, padx=(0, 20))

        # Apply filter button
        tbs.Button(filter_frame, text="Apply Filters",
                  command=lambda: self.filter_work_orders(status_var.get(), date_var.get()),
                  bootstyle="primary").pack(side=tk.LEFT)

        # Work orders list
        list_frame = tbs.Frame(content_frame, padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # Create treeview
        columns = ("ID", "Date", "Location", "Type", "Status", "Priority", "Actions")
        orders_tree = tbs.Treeview(list_frame, columns=columns, show="headings")

        # Define column headings
        for col in columns:
            orders_tree.heading(col, text=col)
            # Set column widths
            if col == "ID":
                orders_tree.column(col, width=50)
            elif col in ["Date", "Status", "Priority"]:
                orders_tree.column(col, width=100)
            elif col == "Actions":
                orders_tree.column(col, width=150)
            else:
                orders_tree.column(col, width=120)

        # Add scrollbar
        scrollbar = tbs.Scrollbar(list_frame, orient=tk.VERTICAL, command=orders_tree.yview)
        orders_tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        orders_tree.pack(fill=tk.BOTH, expand=True)

        # Sample data
        my_orders = [
            (1, "2023-11-15", "Guest Room 101", "Plumbing", "In Progress", "Medium", "View"),
            (2, "2023-11-14", "Kitchen", "Electrical", "Pending", "High", "View"),
            (3, "2023-11-12", "Bar", "Appliance", "Completed", "High", "View"),
            (4, "2023-11-10", "Gym", "Equipment", "In Progress", "Low", "View"),
            (5, "2023-11-08", "Guest Room 103", "Furniture", "Completed", "Medium", "View")
        ]

        for item in my_orders:
            orders_tree.insert("", tk.END, values=item)

    def filter_work_orders(self, status, date_range):
        """Filter work orders by status and date range"""
        messagebox.showinfo("Filter Applied", f"Filtering by Status: {status}, Date Range: {date_range}")
        # In a real app, this would reload the data with filters applied

    def show_all_requests(self):
        """Show all work order requests (admin view)"""
        self.show_frame(self.all_requests_frame)

        # Clear existing widgets
        for widget in self.all_requests_frame.winfo_children():
            widget.destroy()

        # Create content
        content_frame = tbs.Frame(self.all_requests_frame, padding=20)
        content_frame.pack(fill=tk.BOTH, expand=True)

        tbs.Label(content_frame, text="All Work Orders",
                 font=("Arial", 24, "bold")).pack(anchor=tk.W, pady=10)

        # This would be similar to my_requests but with more admin options
        tbs.Label(content_frame, text="Admin view of all work orders coming soon...",
                 font=("Arial", 14)).pack(pady=50)

    def show_reports(self):
        """Show reports and analytics"""
        self.show_frame(self.reports_frame)

        # Clear existing widgets
        for widget in self.reports_frame.winfo_children():
            widget.destroy()

        # Create content
        content_frame = tbs.Frame(self.reports_frame, padding=20)
        content_frame.pack(fill=tk.BOTH, expand=True)

        tbs.Label(content_frame, text="Maintenance Reports",
                 font=("Arial", 24, "bold")).pack(anchor=tk.W, pady=10)

        # This would show reports and analytics
        tbs.Label(content_frame, text="Reports and analytics coming soon...",
                 font=("Arial", 14)).pack(pady=50)

    def logout(self):
        """Log out and return to login screen"""
        if messagebox.askyesno("Logout", "Are you sure you want to logout?"):
            self.root.destroy()
            # In a real app, you would launch the login screen here
            import login
            root = tk.Tk()
            login.LoginApp(root)
            root.mainloop()

if __name__ == "__main__":
    # This is just for testing the work order system directly
    root = tk.Tk()
    WorkOrderSystem(root)
    root.mainloop()
