import tkinter as tk
from tkinter import ttk, messagebox
import datetime
import json
import threading

class SocialSecretaryPortal:
    def __init__(self, root):
        self.root = root
        self.root.title("Social Secretary Portal - Command Facilities Manager")
        self.root.geometry("1200x800")
        self.root.configure(bg="#f8fafc")
        
        # Initialize data
        self.events = []
        self.honorary_members = [
            {"name": "Dr. <PERSON>", "birthday": "1975-03-15", "profession": "Medical Doctor", "join_date": "2020-01-15"},
            {"name": "Eng. <PERSON>", "birthday": "1980-07-22", "profession": "Civil Engineer", "join_date": "2019-06-20"},
            {"name": "Prof<PERSON> <PERSON>", "birthday": "1970-11-08", "profession": "University Professor", "join_date": "2021-03-10"},
            {"name": "Mr. <PERSON>", "birthday": "1985-12-03", "profession": "Business Executive", "join_date": "2022-08-05"},
            {"name": "<PERSON>. <PERSON>", "birthday": "1978-09-18", "profession": "Veterinarian", "join_date": "2023-02-12"}
        ]
        
        self.announcements = []
        
        self.setup_ui()
        self.check_birthdays()
        self.start_auto_updates()
        
    def setup_ui(self):
        """Setup the social secretary portal UI"""
        # Header
        header_frame = tk.Frame(self.root, bg="#1e40af", height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="🎉 SOCIAL SECRETARY PORTAL", 
                font=("Arial", 18, "bold"), fg="white", bg="#1e40af").pack(pady=15)
        tk.Label(header_frame, text="Event Management & Member Celebrations", 
                font=("Arial", 11), fg="#bfdbfe", bg="#1e40af").pack()
        
        # Main content
        main_frame = tk.Frame(self.root, bg="#f8fafc")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Create notebook
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Events tab
        self.events_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.events_frame, text="🎊 Events & Happy Hour")
        
        # Birthdays tab
        self.birthdays_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.birthdays_frame, text="🎂 Birthday Celebrations")
        
        # Announcements tab
        self.announcements_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.announcements_frame, text="📢 Announcements")
        
        # Dashboard tab
        self.dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.dashboard_frame, text="📊 Dashboard")
        
        self.setup_events_tab()
        self.setup_birthdays_tab()
        self.setup_announcements_tab()
        self.setup_dashboard_tab()
        
    def setup_events_tab(self):
        """Setup events and happy hour tab"""
        # Quick actions
        quick_frame = ttk.LabelFrame(self.events_frame, text="Quick Actions", padding=15)
        quick_frame.pack(fill=tk.X, padx=20, pady=10)
        
        buttons_frame = tk.Frame(quick_frame)
        buttons_frame.pack(fill=tk.X)
        
        tk.Button(buttons_frame, text="🍻 Announce Happy Hour", bg="#f59e0b", fg="white",
                 font=("Arial", 11, "bold"), command=self.announce_happy_hour).pack(side=tk.LEFT, padx=5)
        tk.Button(buttons_frame, text="🎉 Create Event", bg="#10b981", fg="white",
                 font=("Arial", 11, "bold"), command=self.create_event).pack(side=tk.LEFT, padx=5)
        tk.Button(buttons_frame, text="📅 Weekly Schedule", bg="#3b82f6", fg="white",
                 font=("Arial", 11, "bold"), command=self.create_weekly_schedule).pack(side=tk.LEFT, padx=5)
        
        # Current events
        current_events_frame = ttk.LabelFrame(self.events_frame, text="Current & Upcoming Events", padding=15)
        current_events_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Events table
        self.events_tree = ttk.Treeview(current_events_frame, columns=("Date", "Time", "Event", "Venue", "Dress Code", "Status"), show="headings", height=10)
        self.events_tree.heading("Date", text="Date")
        self.events_tree.heading("Time", text="Time")
        self.events_tree.heading("Event", text="Event")
        self.events_tree.heading("Venue", text="Venue")
        self.events_tree.heading("Dress Code", text="Dress Code")
        self.events_tree.heading("Status", text="Status")
        
        self.events_tree.column("Date", width=100)
        self.events_tree.column("Time", width=80)
        self.events_tree.column("Event", width=200)
        self.events_tree.column("Venue", width=150)
        self.events_tree.column("Dress Code", width=120)
        self.events_tree.column("Status", width=100)
        
        # Sample events
        sample_events = [
            (datetime.datetime.now().strftime("%Y-%m-%d"), "18:00", "Happy Hour", "Hub Mess Bar", "Casual", "Active"),
            ((datetime.datetime.now() + datetime.timedelta(days=2)).strftime("%Y-%m-%d"), "19:00", "Monthly Dinner", "Main Hall", "Formal", "Planned"),
            ((datetime.datetime.now() + datetime.timedelta(days=7)).strftime("%Y-%m-%d"), "17:00", "Wine Tasting", "Hub Mess Bar", "Smart Casual", "Planned")
        ]
        
        for event in sample_events:
            self.events_tree.insert("", tk.END, values=event)
        
        self.events_tree.pack(fill=tk.BOTH, expand=True)
        
        # Event actions
        event_actions_frame = tk.Frame(current_events_frame)
        event_actions_frame.pack(fill=tk.X, pady=10)
        
        tk.Button(event_actions_frame, text="✏️ Edit Event", command=self.edit_event).pack(side=tk.LEFT, padx=5)
        tk.Button(event_actions_frame, text="📢 Send Reminder", command=self.send_event_reminder).pack(side=tk.LEFT, padx=5)
        tk.Button(event_actions_frame, text="❌ Cancel Event", command=self.cancel_event).pack(side=tk.LEFT, padx=5)
        
    def setup_birthdays_tab(self):
        """Setup birthday celebrations tab"""
        # Birthday alerts
        alerts_frame = ttk.LabelFrame(self.birthdays_frame, text="🎂 Birthday Alerts", padding=15)
        alerts_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.birthday_alerts_text = tk.Text(alerts_frame, height=4, font=("Arial", 10))
        self.birthday_alerts_text.pack(fill=tk.X)
        
        # Birthday calendar
        calendar_frame = ttk.LabelFrame(self.birthdays_frame, text="Birthday Calendar", padding=15)
        calendar_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Birthday table
        self.birthday_tree = ttk.Treeview(calendar_frame, columns=("Name", "Birthday", "Age", "Profession", "Days Until", "Action"), show="headings", height=12)
        self.birthday_tree.heading("Name", text="Name")
        self.birthday_tree.heading("Birthday", text="Birthday")
        self.birthday_tree.heading("Age", text="Age")
        self.birthday_tree.heading("Profession", text="Profession")
        self.birthday_tree.heading("Days Until", text="Days Until")
        self.birthday_tree.heading("Action", text="Action")
        
        self.birthday_tree.column("Name", width=150)
        self.birthday_tree.column("Birthday", width=100)
        self.birthday_tree.column("Age", width=60)
        self.birthday_tree.column("Profession", width=150)
        self.birthday_tree.column("Days Until", width=80)
        self.birthday_tree.column("Action", width=100)
        
        self.update_birthday_calendar()
        self.birthday_tree.pack(fill=tk.BOTH, expand=True)
        
        # Birthday actions
        birthday_actions_frame = tk.Frame(calendar_frame)
        birthday_actions_frame.pack(fill=tk.X, pady=10)
        
        tk.Button(birthday_actions_frame, text="🎉 Celebrate Birthday", bg="#ec4899", fg="white",
                 font=("Arial", 11, "bold"), command=self.celebrate_birthday).pack(side=tk.LEFT, padx=5)
        tk.Button(birthday_actions_frame, text="📧 Send Birthday Wishes", bg="#8b5cf6", fg="white",
                 command=self.send_birthday_wishes).pack(side=tk.LEFT, padx=5)
        tk.Button(birthday_actions_frame, text="🎂 Plan Birthday Event", bg="#f59e0b", fg="white",
                 command=self.plan_birthday_event).pack(side=tk.LEFT, padx=5)
        
    def setup_announcements_tab(self):
        """Setup announcements tab"""
        # Create announcement
        create_frame = ttk.LabelFrame(self.announcements_frame, text="Create New Announcement", padding=15)
        create_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Announcement form
        form_frame = tk.Frame(create_frame)
        form_frame.pack(fill=tk.X)
        
        tk.Label(form_frame, text="Title:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.announcement_title = tk.StringVar()
        tk.Entry(form_frame, textvariable=self.announcement_title, width=50).grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        tk.Label(form_frame, text="Type:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.announcement_type = tk.StringVar(value="General")
        type_combo = ttk.Combobox(form_frame, textvariable=self.announcement_type, width=15)
        type_combo['values'] = ('General', 'Event', 'Happy Hour', 'Birthday', 'Emergency', 'Social')
        type_combo.grid(row=0, column=3, padx=5, pady=5)
        
        tk.Label(form_frame, text="Message:").grid(row=1, column=0, sticky=tk.NW, padx=5, pady=5)
        self.announcement_message = tk.Text(form_frame, height=4, width=60)
        self.announcement_message.grid(row=1, column=1, columnspan=3, padx=5, pady=5, sticky=tk.W)
        
        tk.Label(form_frame, text="Priority:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.announcement_priority = tk.StringVar(value="Normal")
        priority_combo = ttk.Combobox(form_frame, textvariable=self.announcement_priority, width=15)
        priority_combo['values'] = ('Low', 'Normal', 'High', 'Urgent')
        priority_combo.grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        
        tk.Button(form_frame, text="📢 Send Announcement", bg="#10b981", fg="white",
                 font=("Arial", 11, "bold"), command=self.send_announcement).grid(row=2, column=3, padx=5, pady=5)
        
        # Recent announcements
        recent_frame = ttk.LabelFrame(self.announcements_frame, text="Recent Announcements", padding=15)
        recent_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        self.announcements_tree = ttk.Treeview(recent_frame, columns=("Time", "Title", "Type", "Priority", "Status"), show="headings", height=10)
        self.announcements_tree.heading("Time", text="Time")
        self.announcements_tree.heading("Title", text="Title")
        self.announcements_tree.heading("Type", text="Type")
        self.announcements_tree.heading("Priority", text="Priority")
        self.announcements_tree.heading("Status", text="Status")
        
        self.announcements_tree.column("Time", width=120)
        self.announcements_tree.column("Title", width=250)
        self.announcements_tree.column("Type", width=100)
        self.announcements_tree.column("Priority", width=80)
        self.announcements_tree.column("Status", width=100)
        
        self.announcements_tree.pack(fill=tk.BOTH, expand=True)
        
    def setup_dashboard_tab(self):
        """Setup dashboard tab"""
        # Statistics
        stats_frame = ttk.LabelFrame(self.dashboard_frame, text="Social Activities Statistics", padding=15)
        stats_frame.pack(fill=tk.X, padx=20, pady=10)
        
        stats_grid = tk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)
        
        # Create stat cards
        self.create_stat_card(stats_grid, 0, 0, "Events This Month", "8", "#10b981")
        self.create_stat_card(stats_grid, 0, 1, "Birthdays This Month", "3", "#f59e0b")
        self.create_stat_card(stats_grid, 0, 2, "Announcements Sent", "15", "#3b82f6")
        self.create_stat_card(stats_grid, 0, 3, "Happy Hours", "12", "#ec4899")
        
        # Configure grid
        for i in range(4):
            stats_grid.columnconfigure(i, weight=1)
        
        # Live feed
        feed_frame = ttk.LabelFrame(self.dashboard_frame, text="Live Activity Feed", padding=15)
        feed_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        self.activity_feed = tk.Text(feed_frame, height=15, font=("Arial", 10))
        self.activity_feed.pack(fill=tk.BOTH, expand=True)
        
        # Update feed
        self.update_activity_feed()
        
    def create_stat_card(self, parent, row, col, title, value, color):
        """Create a statistics card"""
        card_frame = tk.Frame(parent, relief=tk.RAISED, borderwidth=2, bg="white")
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
        
        tk.Label(card_frame, text=value, font=("Arial", 24, "bold"), fg=color, bg="white").pack(pady=5)
        tk.Label(card_frame, text=title, font=("Arial", 10), bg="white").pack(pady=5)
        
    def announce_happy_hour(self):
        """Announce happy hour"""
        happy_hour_window = tk.Toplevel(self.root)
        happy_hour_window.title("Announce Happy Hour")
        happy_hour_window.geometry("500x400")
        happy_hour_window.transient(self.root)
        happy_hour_window.grab_set()
        
        # Header
        tk.Label(happy_hour_window, text="🍻 HAPPY HOUR ANNOUNCEMENT", 
                font=("Arial", 16, "bold")).pack(pady=10)
        
        # Form
        form_frame = ttk.LabelFrame(happy_hour_window, text="Happy Hour Details", padding=15)
        form_frame.pack(fill=tk.X, padx=20, pady=10)
        
        tk.Label(form_frame, text="Date:").grid(row=0, column=0, sticky=tk.W, pady=5)
        date_var = tk.StringVar(value=datetime.datetime.now().strftime("%Y-%m-%d"))
        tk.Entry(form_frame, textvariable=date_var, width=15).grid(row=0, column=1, pady=5, sticky=tk.W)
        
        tk.Label(form_frame, text="Start Time:").grid(row=1, column=0, sticky=tk.W, pady=5)
        start_time_var = tk.StringVar(value="18:00")
        tk.Entry(form_frame, textvariable=start_time_var, width=15).grid(row=1, column=1, pady=5, sticky=tk.W)
        
        tk.Label(form_frame, text="End Time:").grid(row=2, column=0, sticky=tk.W, pady=5)
        end_time_var = tk.StringVar(value="20:00")
        tk.Entry(form_frame, textvariable=end_time_var, width=15).grid(row=2, column=1, pady=5, sticky=tk.W)
        
        tk.Label(form_frame, text="Dress Code:").grid(row=3, column=0, sticky=tk.W, pady=5)
        dress_code_var = tk.StringVar(value="Casual")
        dress_combo = ttk.Combobox(form_frame, textvariable=dress_code_var, width=15)
        dress_combo['values'] = ('Casual', 'Smart Casual', 'Formal', 'Traditional')
        dress_combo.grid(row=3, column=1, pady=5, sticky=tk.W)
        
        tk.Label(form_frame, text="Special Offers:").grid(row=4, column=0, sticky=tk.NW, pady=5)
        offers_text = tk.Text(form_frame, height=4, width=40)
        offers_text.grid(row=4, column=1, columnspan=2, pady=5, sticky=tk.W)
        offers_text.insert("1.0", "• Buy 2 Get 1 Free on selected beers\n• 20% off wine\n• Complimentary snacks")
        
        # Buttons
        button_frame = tk.Frame(happy_hour_window)
        button_frame.pack(fill=tk.X, padx=20, pady=20)
        
        tk.Button(button_frame, text="📢 Announce Now", bg="#f59e0b", fg="white",
                 font=("Arial", 12, "bold"), 
                 command=lambda: self.send_happy_hour_announcement(
                     date_var.get(), start_time_var.get(), end_time_var.get(), 
                     dress_code_var.get(), offers_text.get("1.0", tk.END), happy_hour_window)).pack(side=tk.RIGHT, padx=5)
        tk.Button(button_frame, text="Cancel", command=happy_hour_window.destroy).pack(side=tk.RIGHT, padx=5)
        
    def send_happy_hour_announcement(self, date, start_time, end_time, dress_code, offers, window):
        """Send happy hour announcement"""
        announcement = {
            'title': f"🍻 HAPPY HOUR - {date}",
            'type': 'Happy Hour',
            'message': f"""Join us for Happy Hour at Hub Mess Bar!

📅 Date: {date}
🕕 Time: {start_time} - {end_time}
👔 Dress Code: {dress_code}

🎉 Special Offers:
{offers}

See you there! 🍻""",
            'priority': 'High',
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'Sent'
        }
        
        self.announcements.append(announcement)
        self.update_announcements_display()
        
        # Simulate sending to all dashboards
        self.broadcast_announcement(announcement)
        
        window.destroy()
        messagebox.showinfo("Happy Hour Announced", 
                           "Happy Hour announcement sent to all members!\n\n"
                           "✅ CSO Dashboard updated\n"
                           "✅ Member notifications sent\n"
                           "✅ Bar staff notified")
        
    def celebrate_birthday(self):
        """Celebrate a member's birthday"""
        selected = self.birthday_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a member to celebrate!")
            return
            
        item = self.birthday_tree.item(selected[0])
        member_name = item['values'][0]
        
        # Create birthday celebration announcement
        announcement = {
            'title': f"🎂 Birthday Celebration - {member_name}",
            'type': 'Birthday',
            'message': f"""🎉 HAPPY BIRTHDAY {member_name.upper()}! 🎉

Join us in celebrating our esteemed Honorary Mess Member!

🎂 Birthday celebration at the Main Hall
🕕 Time: 19:00 today
🎁 Cake cutting ceremony
🥂 Toast in honor of {member_name}

Let's make this day special! 🎊""",
            'priority': 'High',
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'Sent'
        }
        
        self.announcements.append(announcement)
        self.update_announcements_display()
        self.broadcast_announcement(announcement)
        
        messagebox.showinfo("Birthday Celebration", 
                           f"Birthday celebration announced for {member_name}!\n\n"
                           "✅ All members notified\n"
                           "✅ Event added to calendar\n"
                           "✅ Cake ordered from kitchen")
        
    def send_announcement(self):
        """Send general announcement"""
        title = self.announcement_title.get()
        message = self.announcement_message.get("1.0", tk.END).strip()
        
        if not title or not message:
            messagebox.showwarning("Missing Information", "Please fill in title and message!")
            return
            
        announcement = {
            'title': title,
            'type': self.announcement_type.get(),
            'message': message,
            'priority': self.announcement_priority.get(),
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'Sent'
        }
        
        self.announcements.append(announcement)
        self.update_announcements_display()
        self.broadcast_announcement(announcement)
        
        # Clear form
        self.announcement_title.set("")
        self.announcement_message.delete("1.0", tk.END)
        
        messagebox.showinfo("Announcement Sent", 
                           "Announcement sent successfully!\n\n"
                           "✅ All dashboards updated\n"
                           "✅ Notifications sent\n"
                           "✅ Activity logged")
        
    def broadcast_announcement(self, announcement):
        """Broadcast announcement to all dashboards (simulate)"""
        # In production, this would update all connected dashboards in real-time
        print(f"Broadcasting: {announcement['title']}")
        
        # Simulate real-time dashboard updates
        def update_dashboards():
            # This would be WebSocket or similar real-time communication
            pass
        
        threading.Thread(target=update_dashboards, daemon=True).start()
        
    def check_birthdays(self):
        """Check for upcoming birthdays"""
        today = datetime.datetime.now().date()
        alerts = []
        
        for member in self.honorary_members:
            birthday = datetime.datetime.strptime(member['birthday'], '%Y-%m-%d').date()
            this_year_birthday = birthday.replace(year=today.year)
            
            if this_year_birthday < today:
                this_year_birthday = birthday.replace(year=today.year + 1)
            
            days_until = (this_year_birthday - today).days
            
            if days_until == 0:
                alerts.append(f"🎂 TODAY: {member['name']}'s birthday!")
            elif days_until <= 7:
                alerts.append(f"🎂 {days_until} days: {member['name']}'s birthday")
        
        if alerts:
            alert_text = "UPCOMING BIRTHDAYS:\n\n" + "\n".join(alerts)
        else:
            alert_text = "No birthdays in the next 7 days."
            
        self.birthday_alerts_text.delete(1.0, tk.END)
        self.birthday_alerts_text.insert(1.0, alert_text)
        
    def update_birthday_calendar(self):
        """Update birthday calendar"""
        # Clear existing items
        for item in self.birthday_tree.get_children():
            self.birthday_tree.delete(item)
            
        today = datetime.datetime.now().date()
        
        for member in self.honorary_members:
            birthday = datetime.datetime.strptime(member['birthday'], '%Y-%m-%d').date()
            this_year_birthday = birthday.replace(year=today.year)
            
            if this_year_birthday < today:
                this_year_birthday = birthday.replace(year=today.year + 1)
            
            age = today.year - birthday.year
            days_until = (this_year_birthday - today).days
            
            if days_until == 0:
                action = "🎉 Celebrate!"
            elif days_until <= 7:
                action = "📅 Plan Event"
            else:
                action = "⏳ Upcoming"
            
            self.birthday_tree.insert("", tk.END, values=(
                member['name'], 
                birthday.strftime('%m-%d'), 
                age, 
                member['profession'], 
                days_until, 
                action
            ))
    
    def update_announcements_display(self):
        """Update announcements display"""
        # Clear existing items
        for item in self.announcements_tree.get_children():
            self.announcements_tree.delete(item)
            
        # Add recent announcements
        for announcement in reversed(self.announcements[-10:]):  # Show last 10
            self.announcements_tree.insert("", tk.END, values=(
                announcement['timestamp'],
                announcement['title'],
                announcement['type'],
                announcement['priority'],
                announcement['status']
            ))
    
    def update_activity_feed(self):
        """Update activity feed"""
        feed_text = """LIVE SOCIAL ACTIVITIES FEED:

🎉 15:30 - Birthday celebration announced for Dr. John Smith
🍻 14:45 - Happy Hour scheduled for tonight 18:00-20:00
📢 14:20 - Monthly dinner reminder sent to all members
🎂 13:15 - Cake ordered for birthday celebration
📅 12:30 - Wine tasting event planned for next Friday
🎊 11:45 - Event decorations arranged for main hall
📧 11:20 - Birthday wishes sent to Eng. Mary Johnson
🍷 10:30 - Special wine selection for happy hour
📢 09:45 - Weekly social calendar published
🎉 09:15 - Member appreciation event announced

All activities are automatically synchronized across all dashboards."""
        
        self.activity_feed.delete(1.0, tk.END)
        self.activity_feed.insert(1.0, feed_text)
        
    def start_auto_updates(self):
        """Start automatic updates"""
        def auto_update():
            self.check_birthdays()
            self.update_birthday_calendar()
            
            # Schedule next update
            self.root.after(60000, auto_update)  # Update every minute
        
        auto_update()
    
    # Placeholder methods for other functions
    def create_event(self):
        messagebox.showinfo("Create Event", "Event creation dialog would open here")
        
    def create_weekly_schedule(self):
        messagebox.showinfo("Weekly Schedule", "Weekly schedule planner would open here")
        
    def edit_event(self):
        messagebox.showinfo("Edit Event", "Event editing dialog would open here")
        
    def send_event_reminder(self):
        messagebox.showinfo("Event Reminder", "Event reminder sent to all members!")
        
    def cancel_event(self):
        messagebox.showinfo("Cancel Event", "Event cancellation dialog would open here")
        
    def send_birthday_wishes(self):
        messagebox.showinfo("Birthday Wishes", "Birthday wishes sent!")
        
    def plan_birthday_event(self):
        messagebox.showinfo("Plan Birthday Event", "Birthday event planning dialog would open here")

if __name__ == "__main__":
    root = tk.Tk()
    app = SocialSecretaryPortal(root)
    root.mainloop()
