import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import datetime
import json
import requests
import webbrowser
from PIL import Image, ImageTk
import os

class OnlineBookingPortal:
    def __init__(self, root):
        self.root = root
        self.root.title("Command Facilities Manager - Online Booking Portal")
        self.root.geometry("1000x700")
        self.root.configure(bg="#f0f8ff")

        # Paystack configuration (Test keys - replace with live keys for production)
        self.paystack_public_key = "pk_test_your_paystack_public_key_here"
        self.paystack_secret_key = "sk_test_your_paystack_secret_key_here"

        # Initialize variables
        self.uploaded_documents = {}
        self.booking_data = {}

        self.setup_ui()

    def setup_ui(self):
        """Setup the online booking portal UI"""
        # Header
        header_frame = tk.Frame(self.root, bg="#1e3a8a", height=100)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # Logo and title
        title_frame = tk.Frame(header_frame, bg="#1e3a8a")
        title_frame.pack(expand=True)

        tk.Label(title_frame, text="🏛️ COMMAND FACILITIES MANAGER",
                font=("Arial", 20, "bold"), fg="white", bg="#1e3a8a").pack(pady=10)
        tk.Label(title_frame, text="Online Guest House Booking Portal",
                font=("Arial", 12), fg="#bfdbfe", bg="#1e3a8a").pack()

        # Main content
        main_frame = tk.Frame(self.root, bg="#f0f8ff")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Create notebook for different sections
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Booking tab
        self.booking_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.booking_frame, text="🏠 Book Room")

        # Payment tab
        self.payment_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.payment_frame, text="💳 Payment")

        # Status tab
        self.status_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.status_frame, text="📋 Booking Status")

        self.setup_booking_tab()
        self.setup_payment_tab()
        self.setup_status_tab()

    def setup_booking_tab(self):
        """Setup the booking form"""
        # Welcome section with enhanced marketing content
        welcome_frame = ttk.LabelFrame(self.booking_frame, text="🏛️ Welcome to HUB GUEST HOUSE - Your Home Away From Home", padding=15)
        welcome_frame.pack(fill=tk.X, padx=20, pady=10)

        welcome_text = """Experience comfort and convenience at the prestigious HUB Guest House!
Located in the serene and secure environment of Headquarters Logistics Command Oghara Barrack.

🏨 PREMIUM ACCOMMODATIONS & WORLD-CLASS AMENITIES:
✅ Secure online payment with Nigerian debit cards & mobile money
✅ Instant booking confirmation with digital receipts
✅ Upload required documents seamlessly
✅ Real-time room availability checking
✅ 24/7 customer support and concierge services

🌟 EXCLUSIVE GUEST FACILITIES & ATTRACTIONS:
🌐 FREE High-Speed WiFi throughout the premises
🏋️ Complimentary access to modern Fitness Gym
🍽️ On-site Mess Restaurant with authentic Nigerian cuisine
🍺 Relaxing Mess Bar with premium beverages
🛡️ Safe and serene military environment with 24/7 security
🚗 Ample parking space for guests
🧹 Daily housekeeping and room service
❄️ Air-conditioned rooms with modern amenities
📺 Cable TV and entertainment systems
🛁 Private bathrooms with hot water

Perfect for military personnel, honorary mess members, and distinguished guests!"""

        tk.Label(welcome_frame, text=welcome_text, justify=tk.LEFT, font=("Arial", 10), fg="#1e3a8a").pack(anchor=tk.W)

        # Room selection
        room_frame = ttk.LabelFrame(self.booking_frame, text="Select Room & Dates", padding=15)
        room_frame.pack(fill=tk.X, padx=20, pady=10)

        # Available rooms display with enhanced amenities
        rooms_display_frame = tk.Frame(room_frame)
        rooms_display_frame.pack(fill=tk.X, pady=10)

        tk.Label(rooms_display_frame, text="🏨 Available Rooms with Premium Amenities:", font=("Arial", 12, "bold"), fg="#1e3a8a").pack(anchor=tk.W)

        # Room cards
        rooms_grid = tk.Frame(rooms_display_frame)
        rooms_grid.pack(fill=tk.X, pady=10)

        # Enhanced room data with amenities
        available_rooms = [
            {
                "number": "102",
                "type": "Standard Double",
                "rate_hm": 10000,
                "rate_guest": 15000,
                "amenities": "❄️ AC • 📺 Cable TV • 🌐 WiFi • 🛁 Private Bath • 🛏️ 2 Beds",
                "description": "Comfortable standard room perfect for short stays"
            },
            {
                "number": "104",
                "type": "Deluxe Double",
                "rate_hm": 10000,
                "rate_guest": 15000,
                "amenities": "❄️ AC • 📺 Cable TV • 🌐 WiFi • 🛁 Private Bath • 🛏️ 2 Beds • 🪑 Sofa",
                "description": "Spacious deluxe room with additional seating area"
            },
            {
                "number": "106",
                "type": "Executive Suite",
                "rate_hm": 12000,
                "rate_guest": 18000,
                "amenities": "❄️ AC • 📺 Cable TV • 🌐 WiFi • 🛁 Private Bath • 🛏️ 3 Beds • 🪑 Sofa • 🏢 Balcony",
                "description": "Premium suite with balcony and executive amenities"
            }
        ]

        self.selected_room = tk.StringVar()

        for i, room in enumerate(available_rooms):
            room_card = tk.Frame(rooms_grid, relief=tk.RAISED, borderwidth=2, bg="#f8fafc", padx=10, pady=10)
            room_card.grid(row=0, column=i, padx=10, pady=5, sticky="ew")

            # Room selection radio button
            radio_frame = tk.Frame(room_card, bg="#f8fafc")
            radio_frame.pack(fill=tk.X)

            tk.Radiobutton(radio_frame, text=f"Room {room['number']}",
                          variable=self.selected_room, value=room['number'],
                          font=("Arial", 12, "bold"), bg="#f8fafc", fg="#1e3a8a",
                          command=self.update_room_selection).pack(anchor=tk.W)

            # Room type and description
            tk.Label(room_card, text=room['type'], font=("Arial", 11, "bold"),
                    bg="#f8fafc", fg="#059669").pack(anchor=tk.W, pady=(2,0))
            tk.Label(room_card, text=room['description'], font=("Arial", 9),
                    bg="#f8fafc", fg="#6b7280", wraplength=200).pack(anchor=tk.W, pady=(2,5))

            # Amenities
            tk.Label(room_card, text="Amenities:", font=("Arial", 9, "bold"),
                    bg="#f8fafc", fg="#374151").pack(anchor=tk.W)
            tk.Label(room_card, text=room['amenities'], font=("Arial", 8),
                    bg="#f8fafc", fg="#6b7280", wraplength=200).pack(anchor=tk.W, pady=(0,5))

            # Pricing
            pricing_frame = tk.Frame(room_card, bg="#e0f2fe", relief=tk.RIDGE, bd=1)
            pricing_frame.pack(fill=tk.X, pady=(5,0))

            tk.Label(pricing_frame, text="💰 Rates per night:", font=("Arial", 9, "bold"),
                    bg="#e0f2fe", fg="#0c4a6e").pack(anchor=tk.W, padx=5, pady=2)
            tk.Label(pricing_frame, text=f"HM/Officers: ₦{room['rate_hm']:,}", font=("Arial", 9),
                    bg="#e0f2fe", fg="#0369a1").pack(anchor=tk.W, padx=5)
            tk.Label(pricing_frame, text=f"Guests: ₦{room['rate_guest']:,}", font=("Arial", 9),
                    bg="#e0f2fe", fg="#0369a1").pack(anchor=tk.W, padx=5, pady=(0,2))

        # Configure grid weights
        for i in range(len(available_rooms)):
            rooms_grid.columnconfigure(i, weight=1)

        # Date selection
        dates_frame = tk.Frame(room_frame)
        dates_frame.pack(fill=tk.X, pady=10)

        tk.Label(dates_frame, text="Check-in Date:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.checkin_var = tk.StringVar(value=datetime.datetime.now().strftime("%Y-%m-%d"))
        tk.Entry(dates_frame, textvariable=self.checkin_var, width=15).grid(row=0, column=1, padx=5)

        tk.Label(dates_frame, text="Check-out Date:").grid(row=0, column=2, sticky=tk.W, padx=5)
        self.checkout_var = tk.StringVar()
        tk.Entry(dates_frame, textvariable=self.checkout_var, width=15).grid(row=0, column=3, padx=5)

        tk.Label(dates_frame, text="Nights:").grid(row=0, column=4, sticky=tk.W, padx=5)
        self.nights_label = tk.Label(dates_frame, text="0", font=("Arial", 10, "bold"))
        self.nights_label.grid(row=0, column=5, padx=5)

        # Bind date change events
        self.checkin_var.trace('w', self.calculate_nights)
        self.checkout_var.trace('w', self.calculate_nights)

        # Guest information
        guest_frame = ttk.LabelFrame(self.booking_frame, text="Guest Information", padding=15)
        guest_frame.pack(fill=tk.X, padx=20, pady=10)

        # Guest status
        tk.Label(guest_frame, text="Your Status:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.guest_status = tk.StringVar()
        status_combo = ttk.Combobox(guest_frame, textvariable=self.guest_status, width=25)
        status_combo['values'] = ('Honorary Mess Member', 'Military Officer', 'General Guest')
        status_combo.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        status_combo.bind('<<ComboboxSelected>>', self.update_pricing)

        # Personal details
        tk.Label(guest_frame, text="Full Name:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.guest_name = tk.StringVar()
        tk.Entry(guest_frame, textvariable=self.guest_name, width=30).grid(row=1, column=1, padx=5, pady=5)

        tk.Label(guest_frame, text="Phone Number:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.guest_phone = tk.StringVar()
        tk.Entry(guest_frame, textvariable=self.guest_phone, width=20).grid(row=1, column=3, padx=5, pady=5)

        tk.Label(guest_frame, text="Email Address:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.guest_email = tk.StringVar()
        tk.Entry(guest_frame, textvariable=self.guest_email, width=30).grid(row=2, column=1, padx=5, pady=5)

        tk.Label(guest_frame, text="ID Number:").grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)
        self.guest_id = tk.StringVar()
        tk.Entry(guest_frame, textvariable=self.guest_id, width=20).grid(row=2, column=3, padx=5, pady=5)

        # Document upload
        docs_frame = ttk.LabelFrame(self.booking_frame, text="Required Documents", padding=15)
        docs_frame.pack(fill=tk.X, padx=20, pady=10)

        tk.Label(docs_frame, text="Please upload the following documents:", font=("Arial", 10, "bold")).pack(anchor=tk.W)

        # Document upload buttons
        doc_buttons_frame = tk.Frame(docs_frame)
        doc_buttons_frame.pack(fill=tk.X, pady=10)

        self.doc_status = {}
        documents = [
            ("ID Card/Passport", "id_card"),
            ("Passport Photograph", "passport_photo"),
            ("Letter of Introduction (if applicable)", "intro_letter")
        ]

        for i, (doc_name, doc_key) in enumerate(documents):
            doc_frame = tk.Frame(doc_buttons_frame)
            doc_frame.grid(row=i, column=0, sticky=tk.W, pady=5)

            tk.Label(doc_frame, text=f"{doc_name}:", width=30, anchor=tk.W).pack(side=tk.LEFT)

            upload_btn = tk.Button(doc_frame, text="Upload",
                                  command=lambda key=doc_key, name=doc_name: self.upload_document(key, name))
            upload_btn.pack(side=tk.LEFT, padx=5)

            status_label = tk.Label(doc_frame, text="❌ Not uploaded", fg="red")
            status_label.pack(side=tk.LEFT, padx=5)
            self.doc_status[doc_key] = status_label

        # Pricing summary
        pricing_frame = ttk.LabelFrame(self.booking_frame, text="Booking Summary", padding=15)
        pricing_frame.pack(fill=tk.X, padx=20, pady=10)

        self.pricing_display = tk.Text(pricing_frame, height=6, width=60, state=tk.DISABLED)
        self.pricing_display.pack(fill=tk.X)

        # Proceed button
        proceed_frame = tk.Frame(self.booking_frame)
        proceed_frame.pack(fill=tk.X, padx=20, pady=20)

        tk.Button(proceed_frame, text="📋 Review Booking & Proceed to Payment",
                 font=("Arial", 12, "bold"), bg="#10b981", fg="white",
                 command=self.proceed_to_payment).pack(side=tk.RIGHT, padx=10)

    def update_room_selection(self):
        """Update room selection and pricing"""
        self.update_pricing()

    def calculate_nights(self, *args):
        """Calculate number of nights"""
        try:
            checkin = datetime.datetime.strptime(self.checkin_var.get(), "%Y-%m-%d")
            checkout = datetime.datetime.strptime(self.checkout_var.get(), "%Y-%m-%d")
            nights = (checkout - checkin).days
            if nights > 0:
                self.nights_label.config(text=str(nights))
                self.update_pricing()
            else:
                self.nights_label.config(text="0")
        except:
            self.nights_label.config(text="0")

    def update_pricing(self, *args):
        """Update pricing display"""
        try:
            if not self.selected_room.get() or not self.guest_status.get():
                return

            # Get room rate based on status
            if self.guest_status.get() in ['Honorary Mess Member', 'Military Officer']:
                if self.selected_room.get() == "106":  # Deluxe room
                    rate = 12000
                else:
                    rate = 10000
            else:
                if self.selected_room.get() == "106":  # Deluxe room
                    rate = 18000
                else:
                    rate = 15000

            nights = int(self.nights_label.cget("text")) if self.nights_label.cget("text").isdigit() else 0

            if nights > 0:
                subtotal = rate * nights
                service_charge = subtotal * 0.05  # 5% service charge
                total = subtotal + service_charge

                pricing_text = f"""BOOKING SUMMARY:

Room: {self.selected_room.get()} ({'Deluxe' if self.selected_room.get() == '106' else 'Standard'})
Guest Status: {self.guest_status.get()}
Rate per night: ₦{rate:,}
Number of nights: {nights}

Subtotal: ₦{subtotal:,}
Service charge (5%): ₦{service_charge:,.0f}
TOTAL AMOUNT: ₦{total:,.0f}

💳 Payment accepted: Verve, Mastercard, Visa, Bank Transfer"""

                self.pricing_display.config(state=tk.NORMAL)
                self.pricing_display.delete(1.0, tk.END)
                self.pricing_display.insert(1.0, pricing_text)
                self.pricing_display.config(state=tk.DISABLED)

                # Store booking data
                self.booking_data = {
                    'room': self.selected_room.get(),
                    'rate': rate,
                    'nights': nights,
                    'subtotal': subtotal,
                    'service_charge': service_charge,
                    'total': total
                }
        except Exception as e:
            print(f"Error updating pricing: {e}")

    def upload_document(self, doc_key, doc_name):
        """Upload document"""
        file_path = filedialog.askopenfilename(
            title=f"Upload {doc_name}",
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.pdf"), ("All files", "*.*")]
        )

        if file_path:
            # Store file path (in production, upload to cloud storage)
            self.uploaded_documents[doc_key] = file_path
            self.doc_status[doc_key].config(text="✅ Uploaded", fg="green")
            messagebox.showinfo("Upload Successful", f"{doc_name} uploaded successfully!")

    def proceed_to_payment(self):
        """Proceed to payment tab"""
        # Validate form
        if not self.validate_booking_form():
            return

        # Switch to payment tab
        self.notebook.select(1)
        self.setup_payment_details()

    def validate_booking_form(self):
        """Validate booking form"""
        if not self.selected_room.get():
            messagebox.showerror("Error", "Please select a room")
            return False

        if not self.guest_status.get():
            messagebox.showerror("Error", "Please select your status")
            return False

        if not all([self.guest_name.get(), self.guest_phone.get(), self.guest_email.get(), self.guest_id.get()]):
            messagebox.showerror("Error", "Please fill in all guest information")
            return False

        if not self.checkout_var.get():
            messagebox.showerror("Error", "Please select check-out date")
            return False

        if int(self.nights_label.cget("text")) <= 0:
            messagebox.showerror("Error", "Check-out date must be after check-in date")
            return False

        # Check if required documents are uploaded
        required_docs = ['id_card', 'passport_photo']
        for doc in required_docs:
            if doc not in self.uploaded_documents:
                messagebox.showerror("Error", f"Please upload {doc.replace('_', ' ').title()}")
                return False

        return True

    def setup_payment_tab(self):
        """Setup payment tab"""
        # Payment header
        payment_header = ttk.LabelFrame(self.payment_frame, text="Secure Payment", padding=15)
        payment_header.pack(fill=tk.X, padx=20, pady=10)

        tk.Label(payment_header, text="💳 Secure Payment with Paystack",
                font=("Arial", 16, "bold")).pack()
        tk.Label(payment_header, text="We accept all Nigerian debit cards (Verve, Mastercard, Visa) and bank transfers",
                font=("Arial", 10)).pack()

        # Payment summary will be populated when user proceeds
        self.payment_summary_frame = ttk.LabelFrame(self.payment_frame, text="Payment Summary", padding=15)
        self.payment_summary_frame.pack(fill=tk.X, padx=20, pady=10)

        # Payment methods
        payment_methods_frame = ttk.LabelFrame(self.payment_frame, text="Payment Methods", padding=15)
        payment_methods_frame.pack(fill=tk.X, padx=20, pady=10)

        methods_text = """✅ Debit Cards (Verve, Mastercard, Visa)
✅ Bank Transfer
✅ USSD Payment
✅ Mobile Money
✅ QR Code Payment

🔒 All payments are secured by Paystack with 256-bit SSL encryption"""

        tk.Label(payment_methods_frame, text=methods_text, justify=tk.LEFT).pack(anchor=tk.W)

        # Payment button
        payment_btn_frame = tk.Frame(self.payment_frame)
        payment_btn_frame.pack(fill=tk.X, padx=20, pady=20)

        self.pay_button = tk.Button(payment_btn_frame, text="💳 Pay Now with Paystack",
                                   font=("Arial", 14, "bold"), bg="#0066cc", fg="white",
                                   command=self.initiate_payment, state=tk.DISABLED)
        self.pay_button.pack(side=tk.RIGHT, padx=10)

    def setup_payment_details(self):
        """Setup payment details when user proceeds"""
        # Clear previous content
        for widget in self.payment_summary_frame.winfo_children():
            widget.destroy()

        # Display booking summary
        summary_text = f"""PAYMENT SUMMARY:

Guest: {self.guest_name.get()}
Room: {self.selected_room.get()}
Check-in: {self.checkin_var.get()}
Check-out: {self.checkout_var.get()}
Nights: {self.booking_data['nights']}

Amount: ₦{self.booking_data['total']:,.0f}"""

        tk.Label(self.payment_summary_frame, text=summary_text,
                font=("Arial", 11), justify=tk.LEFT).pack(anchor=tk.W)

        # Enable payment button
        self.pay_button.config(state=tk.NORMAL)

    def initiate_payment(self):
        """Initiate Paystack payment"""
        try:
            # Prepare payment data
            payment_data = {
                "email": self.guest_email.get(),
                "amount": int(self.booking_data['total'] * 100),  # Paystack expects amount in kobo
                "currency": "NGN",
                "reference": f"CFM-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}",
                "callback_url": "http://localhost:8000/payment/callback",
                "metadata": {
                    "guest_name": self.guest_name.get(),
                    "room": self.selected_room.get(),
                    "checkin": self.checkin_var.get(),
                    "checkout": self.checkout_var.get(),
                    "nights": self.booking_data['nights']
                }
            }

            # In production, you would make actual API call to Paystack
            # For demo, we'll simulate the payment process
            self.simulate_payment(payment_data)

        except Exception as e:
            messagebox.showerror("Payment Error", f"Error initiating payment: {str(e)}")

    def simulate_payment(self, payment_data):
        """Simulate payment process (replace with actual Paystack integration)"""
        # Show payment processing dialog
        processing_window = tk.Toplevel(self.root)
        processing_window.title("Processing Payment")
        processing_window.geometry("400x200")
        processing_window.transient(self.root)
        processing_window.grab_set()

        tk.Label(processing_window, text="💳 Processing Payment...",
                font=("Arial", 14, "bold")).pack(pady=20)
        tk.Label(processing_window, text=f"Amount: ₦{self.booking_data['total']:,.0f}",
                font=("Arial", 12)).pack()
        tk.Label(processing_window, text="Please wait while we process your payment",
                font=("Arial", 10)).pack(pady=10)

        # Simulate processing time
        self.root.after(3000, lambda: self.complete_payment(processing_window, payment_data))

    def complete_payment(self, processing_window, payment_data):
        """Complete payment process"""
        processing_window.destroy()

        # Show success message with WiFi access
        messagebox.showinfo("Payment Successful",
                           f"🎉 Payment of ₦{self.booking_data['total']:,.0f} successful!\n\n"
                           f"Reference: {payment_data['reference']}\n"
                           f"Booking confirmed for Room {self.selected_room.get()}\n\n"
                           f"✅ Confirmation details sent to {self.guest_email.get()}\n"
                           f"🌐 WiFi access code will be generated automatically\n"
                           f"📱 Download our app for exclusive services!")

        # Switch to status tab
        self.notebook.select(2)
        self.show_booking_confirmation(payment_data)

    def setup_status_tab(self):
        """Setup booking status tab"""
        status_header = ttk.LabelFrame(self.status_frame, text="Booking Status", padding=15)
        status_header.pack(fill=tk.X, padx=20, pady=10)

        tk.Label(status_header, text="📋 Track Your Booking",
                font=("Arial", 16, "bold")).pack()

        self.status_content = tk.Frame(self.status_frame)
        self.status_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

    def show_booking_confirmation(self, payment_data):
        """Show booking confirmation"""
        # Clear previous content
        for widget in self.status_content.winfo_children():
            widget.destroy()

        # Confirmation details
        confirmation_frame = ttk.LabelFrame(self.status_content, text="Booking Confirmed ✅", padding=15)
        confirmation_frame.pack(fill=tk.X, pady=10)

        confirmation_text = f"""BOOKING CONFIRMATION

Reference: {payment_data['reference']}
Guest: {self.guest_name.get()}
Room: {self.selected_room.get()}
Check-in: {self.checkin_var.get()} (from 2:00 PM)
Check-out: {self.checkout_var.get()} (by 12:00 PM)
Nights: {self.booking_data['nights']}
Amount Paid: ₦{self.booking_data['total']:,.0f}

Status: CONFIRMED ✅
Payment: SUCCESSFUL ✅
Documents: UPLOADED ✅

Next Steps:
1. Check your email for detailed confirmation
2. Arrive at guest house on check-in date
3. Present ID at reception
4. Enjoy your stay!

Contact: +234-XXX-XXXX-XXX for any inquiries"""

        tk.Label(confirmation_frame, text=confirmation_text,
                font=("Arial", 10), justify=tk.LEFT).pack(anchor=tk.W)

        # Action buttons with enhanced features
        actions_frame = tk.Frame(self.status_content)
        actions_frame.pack(fill=tk.X, pady=20)

        tk.Button(actions_frame, text="📧 Email Confirmation",
                 command=lambda: messagebox.showinfo("Email", "Confirmation sent to your email!")).pack(side=tk.LEFT, padx=5)
        tk.Button(actions_frame, text="📱 SMS Confirmation",
                 command=lambda: messagebox.showinfo("SMS", "Confirmation sent via SMS!")).pack(side=tk.LEFT, padx=5)
        tk.Button(actions_frame, text="🖨️ Print Receipt",
                 command=lambda: messagebox.showinfo("Print", "Receipt sent to printer!")).pack(side=tk.LEFT, padx=5)

        # WiFi QR Code button
        tk.Button(actions_frame, text="🌐 Get WiFi Access",
                 command=self.open_wifi_qr_system, bg="#10b981", fg="white",
                 font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)

        # App download button
        tk.Button(actions_frame, text="📱 Download App",
                 command=self.promote_app_download, bg="#3b82f6", fg="white",
                 font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)

        tk.Button(actions_frame, text="🏠 New Booking",
                 command=self.new_booking).pack(side=tk.RIGHT, padx=5)

    def new_booking(self):
        """Start new booking"""
        # Reset form
        self.selected_room.set("")
        self.guest_status.set("")
        self.guest_name.set("")
        self.guest_phone.set("")
        self.guest_email.set("")
        self.guest_id.set("")
        self.checkout_var.set("")
        self.uploaded_documents.clear()

        # Reset document status
        for status_label in self.doc_status.values():
            status_label.config(text="❌ Not uploaded", fg="red")

        # Clear pricing
        self.pricing_display.config(state=tk.NORMAL)
        self.pricing_display.delete(1.0, tk.END)
        self.pricing_display.config(state=tk.DISABLED)

        # Switch to booking tab
        self.notebook.select(0)

if __name__ == "__main__":
    root = tk.Tk()
    app = OnlineBookingPortal(root)
    root.mainloop()
