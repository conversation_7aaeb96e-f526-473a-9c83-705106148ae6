import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
import random
import string
import datetime
import threading
import time
from PIL import Image, ImageTk
import enhanced_db as db

class LoginApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Command Facilities Manager")
        self.root.geometry("900x600")
        self.root.resizable(True, True)

        # Set background color
        self.root.configure(bg="#f0f0f0")

        # Create a database connection
        self.conn = sqlite3.connect('hq_logistics.db')
        self.cur = self.conn.cursor()

        # Initialize the database if needed
        self.initialize_db()

        # Create main frames
        self.create_frames()

        # Show login frame by default
        self.show_frame(self.login_frame)

    def initialize_db(self):
        """Initialize the database if needed"""
        # Check if users table exists
        self.cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not self.cur.fetchone():
            # Create users table
            self.cur.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    role TEXT NOT NULL,
                    email TEXT,
                    phone TEXT,
                    last_login TEXT,
                    status TEXT DEFAULT 'Active'
                )
            """)

            # Create default admin user
            self.cur.execute("""
                INSERT INTO users (username, password, full_name, role, email, phone)
                VALUES (?, ?, ?, ?, ?, ?)
            """, ("admin", "admin123", "Chief Security Officer", "CSO", "<EMAIL>", "08000000000"))

            self.conn.commit()

    def create_frames(self):
        """Create all frames for the application"""
        # Main container
        self.container = ttk.Frame(self.root)
        self.container.pack(fill=tk.BOTH, expand=True)

        # Login frame
        self.login_frame = ttk.Frame(self.container)
        self.setup_login_frame()

        # Registration frame
        self.register_frame = ttk.Frame(self.container)
        self.setup_register_frame()

        # Verification frame
        self.verify_frame = ttk.Frame(self.container)
        self.setup_verify_frame()

        # Role selection frame
        self.role_frame = ttk.Frame(self.container)
        self.setup_role_frame()

    def show_frame(self, frame):
        """Show the specified frame and hide others"""
        for f in [self.login_frame, self.register_frame, self.verify_frame, self.role_frame]:
            f.pack_forget()

        frame.pack(fill=tk.BOTH, expand=True)

    def setup_login_frame(self):
        """Setup the login frame"""
        # Create a style
        style = ttk.Style()
        style.configure('TLabel', font=('Arial', 12))
        style.configure('TButton', font=('Arial', 12))
        style.configure('TEntry', font=('Arial', 12))

        # Create left panel for logo and info
        left_panel = ttk.Frame(self.login_frame, width=400)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Logo placeholder
        logo_frame = ttk.Frame(left_panel, width=300, height=200)
        logo_frame.pack(pady=30)

        # Try to load logo if it exists
        try:
            logo_img = Image.open("HQ LOC logo.jpg")
            logo_img = logo_img.resize((300, 200), Image.LANCZOS)
            logo_photo = ImageTk.PhotoImage(logo_img)
            logo_label = ttk.Label(logo_frame, image=logo_photo)
            logo_label.image = logo_photo
            logo_label.pack()
        except:
            # If logo not found, use text instead
            ttk.Label(logo_frame, text="COMMAND FACILITIES", font=("Arial", 20, "bold")).pack(pady=20)

        # Welcome text
        ttk.Label(left_panel, text="Welcome to Command Facilities Manager", font=("Arial", 16, "bold")).pack(pady=10)
        ttk.Label(left_panel, text="Your one-stop solution for:", font=("Arial", 12)).pack(pady=5)

        features = ["• Guest House Management", "• Restaurant & Bar Services",
                   "• Welfare Shop", "• Gym Membership", "• Maintenance Requests"]

        for feature in features:
            ttk.Label(left_panel, text=feature, font=("Arial", 12)).pack(pady=2)

        # Right panel for login form
        right_panel = ttk.Frame(self.login_frame, width=400)
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Login form
        form_frame = ttk.Frame(right_panel, padding=20)
        form_frame.pack(pady=50)

        ttk.Label(form_frame, text="Login to Your Account", font=("Arial", 18, "bold")).pack(pady=20)

        # Username field
        ttk.Label(form_frame, text="Username or Email:").pack(anchor=tk.W, pady=(10, 5))
        self.username_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.username_var, width=30).pack(fill=tk.X, pady=5)

        # Password field
        ttk.Label(form_frame, text="Password:").pack(anchor=tk.W, pady=(10, 5))
        self.password_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.password_var, show="*", width=30).pack(fill=tk.X, pady=5)

        # Login button
        ttk.Button(form_frame, text="Login", command=self.login).pack(fill=tk.X, pady=20)

        # Register link
        register_frame = ttk.Frame(form_frame)
        register_frame.pack(fill=tk.X, pady=10)

        ttk.Label(register_frame, text="Don't have an account?").pack(side=tk.LEFT)
        register_link = ttk.Label(register_frame, text="Register", foreground="blue", cursor="hand2")
        register_link.pack(side=tk.LEFT, padx=5)
        register_link.bind("<Button-1>", lambda e: self.show_frame(self.register_frame))

    def setup_register_frame(self):
        """Setup the registration frame"""
        # Title
        ttk.Label(self.register_frame, text="Create New Account", font=("Arial", 18, "bold")).pack(pady=20)

        # Form frame
        form_frame = ttk.Frame(self.register_frame, padding=20)
        form_frame.pack(pady=10)

        # Full name
        ttk.Label(form_frame, text="Full Name:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.reg_name_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.reg_name_var, width=30).grid(row=0, column=1, pady=5)

        # Email
        ttk.Label(form_frame, text="Email:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.reg_email_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.reg_email_var, width=30).grid(row=1, column=1, pady=5)

        # Phone
        ttk.Label(form_frame, text="Phone:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.reg_phone_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.reg_phone_var, width=30).grid(row=2, column=1, pady=5)

        # Password
        ttk.Label(form_frame, text="Password:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.reg_password_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.reg_password_var, show="*", width=30).grid(row=3, column=1, pady=5)

        # Confirm Password
        ttk.Label(form_frame, text="Confirm Password:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.reg_confirm_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.reg_confirm_var, show="*", width=30).grid(row=4, column=1, pady=5)

        # User type selection
        ttk.Label(form_frame, text="User Type:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.user_type_var = tk.StringVar()
        user_types = ttk.Combobox(form_frame, textvariable=self.user_type_var, width=27)
        user_types['values'] = ('Military Personnel', 'Honorary Mess Member', 'General Guest')
        user_types.current(2)
        user_types.grid(row=5, column=1, pady=5)

        # Register button
        ttk.Button(form_frame, text="Register", command=self.register).grid(row=6, column=0, columnspan=2, pady=20)

        # Back to login link
        back_frame = ttk.Frame(form_frame)
        back_frame.grid(row=7, column=0, columnspan=2, pady=10)

        ttk.Label(back_frame, text="Already have an account?").pack(side=tk.LEFT)
        login_link = ttk.Label(back_frame, text="Login", foreground="blue", cursor="hand2")
        login_link.pack(side=tk.LEFT, padx=5)
        login_link.bind("<Button-1>", lambda e: self.show_frame(self.login_frame))

    def setup_verify_frame(self):
        """Setup the verification frame"""
        # Title
        ttk.Label(self.verify_frame, text="Verify Your Account", font=("Arial", 18, "bold")).pack(pady=20)

        # Info text
        ttk.Label(self.verify_frame, text="A verification code has been sent to your email/phone.").pack(pady=10)
        ttk.Label(self.verify_frame, text="Please enter the code below to complete registration.").pack(pady=5)

        # Verification code entry
        code_frame = ttk.Frame(self.verify_frame, padding=20)
        code_frame.pack(pady=20)

        ttk.Label(code_frame, text="Verification Code:").pack(anchor=tk.W, pady=5)
        self.verification_code_var = tk.StringVar()
        ttk.Entry(code_frame, textvariable=self.verification_code_var, width=20).pack(fill=tk.X, pady=5)

        # Verify button
        ttk.Button(code_frame, text="Verify", command=self.verify_code).pack(fill=tk.X, pady=20)

        # Resend code link
        resend_frame = ttk.Frame(code_frame)
        resend_frame.pack(fill=tk.X, pady=10)

        ttk.Label(resend_frame, text="Didn't receive the code?").pack(side=tk.LEFT)
        resend_link = ttk.Label(resend_frame, text="Resend", foreground="blue", cursor="hand2")
        resend_link.pack(side=tk.LEFT, padx=5)
        resend_link.bind("<Button-1>", lambda e: self.send_verification_code())

    def setup_role_frame(self):
        """Setup the role selection frame for military personnel"""
        # Title
        ttk.Label(self.role_frame, text="Military Personnel Details", font=("Arial", 18, "bold")).pack(pady=20)

        # Form frame
        form_frame = ttk.Frame(self.role_frame, padding=20)
        form_frame.pack(pady=10)

        # Service branch
        ttk.Label(form_frame, text="Service Branch:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.service_branch_var = tk.StringVar()
        service_branch = ttk.Combobox(form_frame, textvariable=self.service_branch_var, width=27)
        service_branch['values'] = ('Navy', 'Army', 'Air Force')
        service_branch.current(0)
        service_branch.grid(row=0, column=1, pady=5)

        # Rank
        ttk.Label(form_frame, text="Rank:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.rank_var = tk.StringVar()
        self.rank_combo = ttk.Combobox(form_frame, textvariable=self.rank_var, width=27)
        self.rank_combo.grid(row=1, column=1, pady=5)

        # Update ranks based on service branch
        service_branch.bind("<<ComboboxSelected>>", self.update_ranks)
        self.update_ranks(None)  # Initialize with Navy ranks

        # Service ID
        ttk.Label(form_frame, text="Service ID:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.service_id_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.service_id_var, width=30).grid(row=2, column=1, pady=5)

        # Submit button
        ttk.Button(form_frame, text="Submit", command=self.submit_military_details).grid(row=3, column=0, columnspan=2, pady=20)

    def update_ranks(self, event):
        """Update ranks based on selected service branch"""
        branch = self.service_branch_var.get()

        if branch == "Navy":
            ranks = [
                "Admiral", "Vice Admiral", "Rear Admiral", "Commodore",
                "Captain", "Commander", "Lieutenant Commander", "Lieutenant",
                "Sub-Lieutenant", "Midshipman"
            ]
        elif branch == "Army":
            ranks = [
                "General", "Lieutenant General", "Major General", "Brigadier General",
                "Colonel", "Lieutenant Colonel", "Major", "Captain",
                "Lieutenant", "Second Lieutenant"
            ]
        elif branch == "Air Force":
            ranks = [
                "Air Marshal", "Air Vice Marshal", "Air Commodore", "Group Captain",
                "Wing Commander", "Squadron Leader", "Flight Lieutenant", "Flying Officer",
                "Pilot Officer"
            ]
        else:
            ranks = []

        self.rank_combo['values'] = ranks
        if ranks:
            self.rank_combo.current(0)

    def login(self):
        """Handle login process"""
        username = self.username_var.get()
        password = self.password_var.get()

        if not username or not password:
            messagebox.showerror("Error", "Please enter both username and password")
            return

        # Check credentials
        self.cur.execute("""
            SELECT id, role, status FROM users
            WHERE (username = ? OR email = ?) AND password = ?
        """, (username, username, password))

        user = self.cur.fetchone()

        if user and user[2] == 'Active':
            # Update last login time
            last_login = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.cur.execute("UPDATE users SET last_login = ? WHERE id = ?", (last_login, user[0]))
            self.conn.commit()

            # Determine which portal to open based on role
            role = user[1]
            if role in ["CSO", "Manager", "Staff"]:
                self.open_admin_portal(user[0], role)
            else:
                self.open_user_portal(user[0], role)
        else:
            messagebox.showerror("Error", "Invalid username or password")

    def register(self):
        """Handle registration process"""
        # Get form values
        name = self.reg_name_var.get()
        email = self.reg_email_var.get()
        phone = self.reg_phone_var.get()
        password = self.reg_password_var.get()
        confirm = self.reg_confirm_var.get()
        user_type = self.user_type_var.get()

        # Validate inputs
        if not all([name, email, phone, password, confirm]):
            messagebox.showerror("Error", "Please fill in all fields")
            return

        if password != confirm:
            messagebox.showerror("Error", "Passwords do not match")
            return

        # Check if email already exists
        self.cur.execute("SELECT COUNT(*) FROM users WHERE email = ?", (email,))
        if self.cur.fetchone()[0] > 0:
            # Generate alternative email suggestion
            alternative = self.suggest_alternative_username(email)
            messagebox.showerror("Error", f"Email already registered. Try {alternative} instead.")
            return

        # Check if username (email) already exists
        self.cur.execute("SELECT COUNT(*) FROM users WHERE username = ?", (email,))
        if self.cur.fetchone()[0] > 0:
            # Generate alternative username suggestion
            alternative = self.suggest_alternative_username(email)
            messagebox.showerror("Error", f"Username already exists. Try {alternative} instead.")
            return

        # Store registration data temporarily
        self.temp_registration = {
            'name': name,
            'email': email,
            'phone': phone,
            'password': password,
            'user_type': user_type
        }

        # Send verification code
        self.send_verification_code()

        # Show verification frame
        self.show_frame(self.verify_frame)

    def suggest_alternative_username(self, username):
        """Generate an alternative username if the current one is taken"""
        # Add a number to the end of the username
        base = username.split('@')[0] if '@' in username else username
        domain = username.split('@')[1] if '@' in username else ""

        i = 1
        while True:
            alternative = f"{base}{i}@{domain}" if domain else f"{base}{i}"

            # Check if alternative exists
            self.cur.execute("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?",
                           (alternative, alternative))
            if self.cur.fetchone()[0] == 0:
                return alternative
            i += 1

    def send_verification_code(self):
        """Generate and send verification code"""
        # Generate a random 6-digit code
        code = ''.join(random.choices(string.digits, k=6))

        # Store the code (in a real app, this would be sent via email/SMS)
        self.verification_code = code

        # In a real app, you would send the code via email or SMS
        # For this demo, we'll just show it in a message box
        messagebox.showinfo("Verification Code", f"Your verification code is: {code}\n\n(In a real app, this would be sent to your email or phone)")

    def verify_code(self):
        """Verify the entered code"""
        entered_code = self.verification_code_var.get()

        if not entered_code:
            messagebox.showerror("Error", "Please enter the verification code")
            return

        if entered_code == self.verification_code:
            # Code is correct, proceed with registration
            user_type = self.temp_registration['user_type']

            if user_type == "Military Personnel":
                # Show military details form
                self.show_frame(self.role_frame)
            else:
                # Complete registration directly
                self.complete_registration()
        else:
            messagebox.showerror("Error", "Invalid verification code")

    def submit_military_details(self):
        """Handle submission of military personnel details"""
        # Get military details
        branch = self.service_branch_var.get()
        rank = self.rank_var.get()
        service_id = self.service_id_var.get()

        if not service_id:
            messagebox.showerror("Error", "Please enter your Service ID")
            return

        # Add military details to registration data
        self.temp_registration['branch'] = branch
        self.temp_registration['rank'] = rank
        self.temp_registration['service_id'] = service_id

        # Complete registration
        self.complete_registration()

    def complete_registration(self):
        """Complete the registration process"""
        # Get registration data
        name = self.temp_registration['name']
        email = self.temp_registration['email']
        phone = self.temp_registration['phone']
        password = self.temp_registration['password']
        user_type = self.temp_registration['user_type']

        try:
            # Check if username/email already exists before attempting to insert
            self.cur.execute("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?", (email, email))
            if self.cur.fetchone()[0] > 0:
                # Generate alternative username
                alternative = self.suggest_alternative_username(email)

                # Ask user if they want to use the alternative
                if messagebox.askyesno("Username Taken",
                                      f"The username/email '{email}' is already taken. Would you like to use '{alternative}' instead?"):
                    email = alternative
                else:
                    # User declined alternative, return to registration form
                    self.show_frame(self.register_frame)
                    return

            # Add user to database
            self.cur.execute("""
                INSERT INTO users (username, password, full_name, role, email, phone)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (email, password, name, user_type, email, phone))

            user_id = self.cur.lastrowid

            # If military personnel, add additional details
            if user_type == "Military Personnel":
                branch = self.temp_registration['branch']
                rank = self.temp_registration['rank']
                service_id = self.temp_registration['service_id']

                # In a real app, you would store these details in a separate table
                # For this demo, we'll just update the user record
                self.cur.execute("""
                    UPDATE users SET
                    branch = ?,
                    rank = ?,
                    service_id = ?
                    WHERE id = ?
                """, (branch, rank, service_id, user_id))

            self.conn.commit()

            messagebox.showinfo("Success", "Registration successful! You can now login.")

            # Clear registration data
            self.temp_registration = {}
            self.verification_code = ""
            self.verification_code_var.set("")

            # Show login frame
            self.show_frame(self.login_frame)

        except sqlite3.IntegrityError as e:
            if "UNIQUE constraint failed: users.username" in str(e):
                # This is the error shown in the screenshot
                alternative = self.suggest_alternative_username(email)
                messagebox.showerror("Registration Failed",
                                    f"Username '{email}' already exists. Try '{alternative}' instead.")

                # Update the email field in the registration form
                self.reg_email_var.set(alternative)
                self.show_frame(self.register_frame)
            else:
                messagebox.showerror("Error", f"Registration failed: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Registration failed: {str(e)}")

    def open_admin_portal(self, user_id, role):
        """Open the admin portal"""
        messagebox.showinfo("Admin Portal", f"Opening Admin Portal for {role}")
        # In a real app, you would launch the admin portal here
        self.root.destroy()
        import hq_logistics
        root = tk.Tk()
        app = hq_logistics.CommandFacilitiesApp()
        root.mainloop()

    def open_user_portal(self, user_id, role):
        """Open the user portal"""
        messagebox.showinfo("User Portal", f"Opening User Portal for {role}")
        # In a real app, you would launch the user portal here
        self.root.destroy()
        import user_portal
        root = tk.Tk()
        app = user_portal.UserPortalApp(root, user_id, role)
        root.mainloop()

if __name__ == "__main__":
    root = tk.Tk()
    app = LoginApp(root)
    root.mainloop()
