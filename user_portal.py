import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import datetime
import os
from PIL import Image, ImageTk
import enhanced_db as db

class UserPortalApp:
    def __init__(self, root, user_id, role):
        self.root = root
        self.user_id = user_id
        self.role = role

        self.root.title("Command Facilities Manager - User Portal")
        self.root.geometry("1200x700")
        self.root.resizable(True, True)

        # Set background color
        self.root.configure(bg="#f0f0f0")

        # Create a database connection
        self.conn = sqlite3.connect('hq_logistics.db')
        self.cur = self.conn.cursor()

        # Get user details
        self.get_user_details()

        # Create main container
        self.container = ttk.Frame(self.root)
        self.container.pack(fill=tk.BOTH, expand=True)

        # Create sidebar and main content area
        self.create_layout()

        # Show dashboard by default
        self.show_dashboard()

    def get_user_details(self):
        """Get user details from database"""
        self.cur.execute("""
            SELECT full_name, email, phone, role
            FROM users
            WHERE id = ?
        """, (self.user_id,))

        user = self.cur.fetchone()
        if user:
            self.user_name = user[0]
            self.user_email = user[1]
            self.user_phone = user[2]
            self.user_role = user[3]
        else:
            self.user_name = "Unknown User"
            self.user_email = ""
            self.user_phone = ""
            self.user_role = self.role

    def create_layout(self):
        """Create the main layout with sidebar and content area"""
        # Create sidebar
        self.sidebar = ttk.Frame(self.container, width=250, style="Sidebar.TFrame")
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y)
        self.sidebar.pack_propagate(False)  # Prevent the sidebar from shrinking

        # Create sidebar header with logo
        header_frame = ttk.Frame(self.sidebar)
        header_frame.pack(fill=tk.X, pady=20)

        # Try to load logo if it exists
        try:
            logo_img = Image.open("HQ LOC logo.jpg")
            logo_img = logo_img.resize((200, 100), Image.LANCZOS)
            logo_photo = ImageTk.PhotoImage(logo_img)
            logo_label = ttk.Label(header_frame, image=logo_photo)
            logo_label.image = logo_photo
            logo_label.pack()
        except:
            # If logo not found, use text instead
            ttk.Label(header_frame, text="COMMAND FACILITIES", font=("Arial", 16, "bold")).pack()
            ttk.Label(header_frame, text="MANAGER", font=("Arial", 16, "bold")).pack()

        # User info
        user_frame = ttk.Frame(self.sidebar)
        user_frame.pack(fill=tk.X, pady=10, padx=10)

        ttk.Label(user_frame, text=f"Welcome,", font=("Arial", 10)).pack(anchor=tk.W)
        ttk.Label(user_frame, text=self.user_name, font=("Arial", 12, "bold")).pack(anchor=tk.W)
        ttk.Label(user_frame, text=self.user_role, font=("Arial", 10, "italic")).pack(anchor=tk.W)

        # Separator
        ttk.Separator(self.sidebar).pack(fill=tk.X, padx=10, pady=10)

        # Navigation menu
        self.create_navigation_menu()

        # Main content area
        self.content = ttk.Frame(self.container)
        self.content.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Create frames for different sections
        self.dashboard_frame = ttk.Frame(self.content)
        self.rooms_frame = ttk.Frame(self.content)
        self.restaurant_frame = ttk.Frame(self.content)
        self.bar_frame = ttk.Frame(self.content)
        self.maintenance_frame = ttk.Frame(self.content)
        self.account_frame = ttk.Frame(self.content)

    def create_navigation_menu(self):
        """Create the navigation menu in the sidebar"""
        # Style for menu buttons
        style = ttk.Style()
        style.configure("Menu.TButton", font=("Arial", 12), padding=10)

        # Menu buttons
        menu_frame = ttk.Frame(self.sidebar)
        menu_frame.pack(fill=tk.X, pady=10)

        # Dashboard button
        dashboard_btn = ttk.Button(menu_frame, text="Dashboard", style="Menu.TButton",
                                  command=self.show_dashboard)
        dashboard_btn.pack(fill=tk.X, pady=2)

        # Rooms button
        rooms_btn = ttk.Button(menu_frame, text="Guest Rooms", style="Menu.TButton",
                              command=self.show_rooms)
        rooms_btn.pack(fill=tk.X, pady=2)

        # Restaurant button
        restaurant_btn = ttk.Button(menu_frame, text="Restaurant", style="Menu.TButton",
                                   command=self.show_restaurant)
        restaurant_btn.pack(fill=tk.X, pady=2)

        # Bar button
        bar_btn = ttk.Button(menu_frame, text="Bar", style="Menu.TButton",
                            command=self.show_bar)
        bar_btn.pack(fill=tk.X, pady=2)

        # Maintenance button
        maintenance_btn = ttk.Button(menu_frame, text="Maintenance Request", style="Menu.TButton",
                                    command=self.show_maintenance)
        maintenance_btn.pack(fill=tk.X, pady=2)

        # Account button
        account_btn = ttk.Button(menu_frame, text="My Account", style="Menu.TButton",
                                command=self.show_account)
        account_btn.pack(fill=tk.X, pady=2)

        # Logout button at the bottom
        ttk.Separator(self.sidebar).pack(fill=tk.X, padx=10, pady=10)
        logout_btn = ttk.Button(self.sidebar, text="Logout", style="Menu.TButton",
                               command=self.logout)
        logout_btn.pack(fill=tk.X, pady=10, padx=10)

    def show_frame(self, frame):
        """Show the specified frame and hide others"""
        for f in [self.dashboard_frame, self.rooms_frame, self.restaurant_frame,
                 self.bar_frame, self.maintenance_frame, self.account_frame]:
            f.pack_forget()

        frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

    def show_dashboard(self):
        """Show the dashboard"""
        self.show_frame(self.dashboard_frame)

        # Clear existing widgets
        for widget in self.dashboard_frame.winfo_children():
            widget.destroy()

        # Create dashboard content
        ttk.Label(self.dashboard_frame, text="Dashboard", font=("Arial", 20, "bold")).pack(pady=10)

        # Create a grid of cards
        cards_frame = ttk.Frame(self.dashboard_frame)
        cards_frame.pack(fill=tk.BOTH, expand=True, pady=20)

        # Configure grid
        for i in range(3):
            cards_frame.columnconfigure(i, weight=1)
        for i in range(2):
            cards_frame.rowconfigure(i, weight=1)

        # Create cards
        self.create_dashboard_card(cards_frame, 0, 0, "Guest Rooms", "View and book available rooms", self.show_rooms)
        self.create_dashboard_card(cards_frame, 0, 1, "Restaurant", "Browse menu and place orders", self.show_restaurant)
        self.create_dashboard_card(cards_frame, 0, 2, "Bar", "Explore our selection of drinks", self.show_bar)
        self.create_dashboard_card(cards_frame, 1, 0, "Maintenance", "Submit maintenance requests", self.show_maintenance)
        self.create_dashboard_card(cards_frame, 1, 1, "My Account", "View your account details", self.show_account)

        # News and updates section
        news_frame = ttk.LabelFrame(self.dashboard_frame, text="News & Updates")
        news_frame.pack(fill=tk.X, pady=20)

        # Sample news items
        news_items = [
            "Welcome to the new HQ Logistics Command Portal!",
            "Restaurant special: 20% off dinner menu this weekend",
            "Bar happy hour: 2 for 1 on selected drinks from 4-6pm",
            "New gym equipment has been installed"
        ]

        for item in news_items:
            ttk.Label(news_frame, text=f"• {item}", font=("Arial", 11)).pack(anchor=tk.W, padx=10, pady=5)

    def create_dashboard_card(self, parent, row, col, title, description, command):
        """Create a card for the dashboard"""
        card = ttk.Frame(parent, style="Card.TFrame", padding=10)
        card.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

        ttk.Label(card, text=title, font=("Arial", 14, "bold")).pack(pady=5)
        ttk.Label(card, text=description, font=("Arial", 10)).pack(pady=5)
        ttk.Button(card, text="Open", command=command).pack(pady=10)

    def show_rooms(self):
        """Show the rooms page"""
        self.show_frame(self.rooms_frame)

        # Clear existing widgets
        for widget in self.rooms_frame.winfo_children():
            widget.destroy()

        # Create rooms content
        ttk.Label(self.rooms_frame, text="Guest Rooms", font=("Arial", 20, "bold")).pack(pady=10)

        # Create a frame for the room cards
        rooms_container = ttk.Frame(self.rooms_frame)
        rooms_container.pack(fill=tk.BOTH, expand=True, pady=20)

        # Get all rooms with their status
        rooms = db.get_guest_house_dashboard()

        # Create a grid layout for room cards
        row = 0
        col = 0

        for room in rooms:
            # Create a frame for each room
            room_frame = ttk.LabelFrame(rooms_container, text=f"Room {room[1]}: {room[2]}")
            room_frame.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

            # Room details
            ttk.Label(room_frame, text=f"Type: {room[2]}").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
            ttk.Label(room_frame, text=f"Capacity: {room[3]} person(s)").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
            ttk.Label(room_frame, text=f"Rate: ₦{room[4]:.2f} per night").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)

            # Status with color coding
            status_frame = ttk.Frame(room_frame)
            status_frame.grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)

            status_color = "#4CAF50" if room[5] == "Available" else "#F44336"  # Green for available, red for occupied
            status_label = ttk.Label(status_frame, text=f"Status: {room[5]}", foreground=status_color, font=("Arial", 10, "bold"))
            status_label.pack(side=tk.LEFT)

            # Add book button for available rooms, or show "Occupied" for unavailable rooms
            if room[5] == "Available":
                ttk.Button(room_frame, text="Book Now",
                          command=lambda r=room[0]: self.book_room(r)).grid(row=4, column=0, pady=5)
            else:
                ttk.Label(room_frame, text="Currently Occupied", foreground="#F44336").grid(row=4, column=0, pady=5)

            # Update grid position
            col += 1
            if col > 2:  # 3 rooms per row
                col = 0
                row += 1

        # Make rows and columns expandable
        for i in range(3):
            rooms_container.columnconfigure(i, weight=1)
        for i in range(row + 1):
            rooms_container.rowconfigure(i, weight=1)

    def book_room(self, room_id):
        """Open room booking dialog"""
        # This would open a booking dialog
        messagebox.showinfo("Book Room", f"Opening booking form for room ID: {room_id}")
        # In a real app, you would open a booking form here

    def show_restaurant(self):
        """Show the restaurant page"""
        self.show_frame(self.restaurant_frame)

        # Clear existing widgets
        for widget in self.restaurant_frame.winfo_children():
            widget.destroy()

        ttk.Label(self.restaurant_frame, text="Restaurant", font=("Arial", 20, "bold")).pack(pady=10)
        ttk.Label(self.restaurant_frame, text="Browse our menu and place orders", font=("Arial", 12)).pack(pady=5)

        # This would show the restaurant menu and ordering system
        # For now, just a placeholder
        ttk.Label(self.restaurant_frame, text="Restaurant menu coming soon...").pack(pady=50)

    def show_bar(self):
        """Show the bar page"""
        self.show_frame(self.bar_frame)

        # Clear existing widgets
        for widget in self.bar_frame.winfo_children():
            widget.destroy()

        ttk.Label(self.bar_frame, text="Bar", font=("Arial", 20, "bold")).pack(pady=10)
        ttk.Label(self.bar_frame, text="Explore our selection of drinks", font=("Arial", 12)).pack(pady=5)

        # This would show the bar menu and ordering system
        # For now, just a placeholder
        ttk.Label(self.bar_frame, text="Bar menu coming soon...").pack(pady=50)

    def show_maintenance(self):
        """Show the maintenance request page"""
        self.show_frame(self.maintenance_frame)

        # Clear existing widgets
        for widget in self.maintenance_frame.winfo_children():
            widget.destroy()

        ttk.Label(self.maintenance_frame, text="Maintenance Request", font=("Arial", 20, "bold")).pack(pady=10)

        # Create form for maintenance request
        form_frame = ttk.Frame(self.maintenance_frame, padding=20)
        form_frame.pack(fill=tk.X, pady=20)

        # Location
        ttk.Label(form_frame, text="Location:").grid(row=0, column=0, sticky=tk.W, pady=5)
        location_var = tk.StringVar()
        location_combo = ttk.Combobox(form_frame, textvariable=location_var, width=30)
        location_combo['values'] = ('Guest Room', 'Restaurant', 'Bar', 'Gym', 'Common Area', 'Other')
        location_combo.current(0)
        location_combo.grid(row=0, column=1, pady=5, sticky=tk.W)

        # Issue type
        ttk.Label(form_frame, text="Issue Type:").grid(row=1, column=0, sticky=tk.W, pady=5)
        issue_var = tk.StringVar()
        issue_combo = ttk.Combobox(form_frame, textvariable=issue_var, width=30)
        issue_combo['values'] = ('Plumbing', 'Electrical', 'Furniture', 'Appliance', 'Cleaning', 'Other')
        issue_combo.current(0)
        issue_combo.grid(row=1, column=1, pady=5, sticky=tk.W)

        # Description
        ttk.Label(form_frame, text="Description:").grid(row=2, column=0, sticky=tk.W, pady=5)
        description_var = tk.StringVar()
        description_text = tk.Text(form_frame, width=40, height=5)
        description_text.grid(row=2, column=1, pady=5, sticky=tk.W)

        # Priority
        ttk.Label(form_frame, text="Priority:").grid(row=3, column=0, sticky=tk.W, pady=5)
        priority_var = tk.StringVar()
        priority_combo = ttk.Combobox(form_frame, textvariable=priority_var, width=30)
        priority_combo['values'] = ('Low', 'Medium', 'High')
        priority_combo.current(1)
        priority_combo.grid(row=3, column=1, pady=5, sticky=tk.W)

        # Submit button
        ttk.Button(form_frame, text="Submit Request",
                  command=lambda: self.submit_maintenance_request(
                      location_var.get(),
                      issue_var.get(),
                      description_text.get("1.0", tk.END),
                      priority_var.get()
                  )).grid(row=4, column=0, columnspan=2, pady=20)

        # Show existing requests
        requests_frame = ttk.LabelFrame(self.maintenance_frame, text="Your Maintenance Requests")
        requests_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # This would show the user's maintenance requests
        # For now, just a placeholder
        ttk.Label(requests_frame, text="No maintenance requests found").pack(pady=20)

    def submit_maintenance_request(self, location, issue_type, description, priority):
        """Submit a maintenance request"""
        if not description.strip():
            messagebox.showerror("Error", "Please provide a description of the issue")
            return

        try:
            # In a real app, you would call the database function to add the request
            # For now, just show a success message
            messagebox.showinfo("Success", "Maintenance request submitted successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to submit request: {str(e)}")

    def show_account(self):
        """Show the account page"""
        self.show_frame(self.account_frame)

        # Clear existing widgets
        for widget in self.account_frame.winfo_children():
            widget.destroy()

        ttk.Label(self.account_frame, text="My Account", font=("Arial", 20, "bold")).pack(pady=10)

        # Create tabs for different account sections
        notebook = ttk.Notebook(self.account_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=10)

        # Profile tab
        profile_tab = ttk.Frame(notebook)
        notebook.add(profile_tab, text="Profile")

        # Bookings tab
        bookings_tab = ttk.Frame(notebook)
        notebook.add(bookings_tab, text="My Bookings")

        # Orders tab
        orders_tab = ttk.Frame(notebook)
        notebook.add(orders_tab, text="My Orders")

        # Setup profile tab
        self.setup_profile_tab(profile_tab)

        # Setup bookings tab (placeholder)
        ttk.Label(bookings_tab, text="Your bookings will appear here", font=("Arial", 12)).pack(pady=50)

        # Setup orders tab (placeholder)
        ttk.Label(orders_tab, text="Your orders will appear here", font=("Arial", 12)).pack(pady=50)

    def setup_profile_tab(self, parent):
        """Setup the profile tab"""
        # Profile information
        info_frame = ttk.Frame(parent, padding=20)
        info_frame.pack(fill=tk.X, pady=10)

        # Name
        ttk.Label(info_frame, text="Name:", font=("Arial", 12, "bold")).grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Label(info_frame, text=self.user_name, font=("Arial", 12)).grid(row=0, column=1, sticky=tk.W, pady=5)

        # Email
        ttk.Label(info_frame, text="Email:", font=("Arial", 12, "bold")).grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Label(info_frame, text=self.user_email, font=("Arial", 12)).grid(row=1, column=1, sticky=tk.W, pady=5)

        # Phone
        ttk.Label(info_frame, text="Phone:", font=("Arial", 12, "bold")).grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Label(info_frame, text=self.user_phone, font=("Arial", 12)).grid(row=2, column=1, sticky=tk.W, pady=5)

        # Role
        ttk.Label(info_frame, text="Role:", font=("Arial", 12, "bold")).grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Label(info_frame, text=self.user_role, font=("Arial", 12)).grid(row=3, column=1, sticky=tk.W, pady=5)

        # Edit profile button
        ttk.Button(info_frame, text="Edit Profile", command=self.edit_profile).grid(row=4, column=0, columnspan=2, pady=20)

        # Change password section
        password_frame = ttk.LabelFrame(parent, text="Change Password", padding=20)
        password_frame.pack(fill=tk.X, pady=10)

        # Current password
        ttk.Label(password_frame, text="Current Password:").grid(row=0, column=0, sticky=tk.W, pady=5)
        current_password_var = tk.StringVar()
        ttk.Entry(password_frame, textvariable=current_password_var, show="*", width=30).grid(row=0, column=1, pady=5)

        # New password
        ttk.Label(password_frame, text="New Password:").grid(row=1, column=0, sticky=tk.W, pady=5)
        new_password_var = tk.StringVar()
        ttk.Entry(password_frame, textvariable=new_password_var, show="*", width=30).grid(row=1, column=1, pady=5)

        # Confirm new password
        ttk.Label(password_frame, text="Confirm New Password:").grid(row=2, column=0, sticky=tk.W, pady=5)
        confirm_password_var = tk.StringVar()
        ttk.Entry(password_frame, textvariable=confirm_password_var, show="*", width=30).grid(row=2, column=1, pady=5)

        # Change password button
        ttk.Button(password_frame, text="Change Password",
                  command=lambda: self.change_password(
                      current_password_var.get(),
                      new_password_var.get(),
                      confirm_password_var.get()
                  )).grid(row=3, column=0, columnspan=2, pady=10)

    def edit_profile(self):
        """Edit user profile"""
        # This would open a profile editing form
        messagebox.showinfo("Edit Profile", "Profile editing functionality coming soon")

    def change_password(self, current_password, new_password, confirm_password):
        """Change user password"""
        if not all([current_password, new_password, confirm_password]):
            messagebox.showerror("Error", "Please fill in all password fields")
            return

        if new_password != confirm_password:
            messagebox.showerror("Error", "New passwords do not match")
            return

        # In a real app, you would verify the current password and update it
        # For now, just show a success message
        messagebox.showinfo("Success", "Password changed successfully")

    def logout(self):
        """Log out and return to login screen"""
        if messagebox.askyesno("Logout", "Are you sure you want to logout?"):
            self.root.destroy()
            # In a real app, you would launch the login screen here
            import login
            root = tk.Tk()
            app = login.LoginApp(root)
            root.mainloop()

if __name__ == "__main__":
    # This is just for testing the user portal directly
    root = tk.Tk()
    app = UserPortalApp(root, 1, "General Guest")
    root.mainloop()
