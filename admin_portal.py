import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import datetime
import os
from PIL import Image, ImageTk
import enhanced_db as db

class AdminPortalApp:
    def __init__(self, root, user_id, role):
        self.root = root
        self.user_id = user_id
        self.role = role

        self.root.title("Command Facilities Manager - Admin Portal")
        self.root.geometry("1200x700")
        self.root.resizable(True, True)

        # Set background color
        self.root.configure(bg="#f0f0f0")

        # Create a database connection
        self.conn = sqlite3.connect('hq_logistics.db')
        self.cur = self.conn.cursor()

        # Get user details
        self.get_user_details()

        # Create main container
        self.container = ttk.Frame(self.root)
        self.container.pack(fill=tk.BOTH, expand=True)

        # Create sidebar and main content area
        self.create_layout()

        # Show dashboard by default
        self.show_dashboard()

    def get_user_details(self):
        """Get user details from database"""
        self.cur.execute("""
            SELECT full_name, email, phone, role
            FROM users
            WHERE id = ?
        """, (self.user_id,))

        user = self.cur.fetchone()
        if user:
            self.user_name = user[0]
            self.user_email = user[1]
            self.user_phone = user[2]
            self.user_role = user[3]
        else:
            self.user_name = "Unknown User"
            self.user_email = ""
            self.user_phone = ""
            self.user_role = self.role

    def create_layout(self):
        """Create the main layout with sidebar and content area"""
        # Create sidebar
        self.sidebar = ttk.Frame(self.container, width=250, style="Sidebar.TFrame")
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y)
        self.sidebar.pack_propagate(False)  # Prevent the sidebar from shrinking

        # Create sidebar header with logo
        header_frame = ttk.Frame(self.sidebar)
        header_frame.pack(fill=tk.X, pady=20)

        # Try to load logo if it exists
        try:
            logo_img = Image.open("HQ LOC logo.jpg")
            logo_img = logo_img.resize((200, 100), Image.LANCZOS)
            logo_photo = ImageTk.PhotoImage(logo_img)
            logo_label = ttk.Label(header_frame, image=logo_photo)
            logo_label.image = logo_photo
            logo_label.pack()
        except:
            # If logo not found, use text instead
            ttk.Label(header_frame, text="COMMAND FACILITIES", font=("Arial", 16, "bold")).pack()
            ttk.Label(header_frame, text="MANAGER", font=("Arial", 16, "bold")).pack()
            ttk.Label(header_frame, text="ADMIN PORTAL", font=("Arial", 14, "bold")).pack()

        # User info
        user_frame = ttk.Frame(self.sidebar)
        user_frame.pack(fill=tk.X, pady=10, padx=10)

        ttk.Label(user_frame, text=f"Welcome,", font=("Arial", 10)).pack(anchor=tk.W)
        ttk.Label(user_frame, text=self.user_name, font=("Arial", 12, "bold")).pack(anchor=tk.W)
        ttk.Label(user_frame, text=self.user_role, font=("Arial", 10, "italic")).pack(anchor=tk.W)

        # Separator
        ttk.Separator(self.sidebar).pack(fill=tk.X, padx=10, pady=10)

        # Navigation menu
        self.create_navigation_menu()

        # Main content area
        self.content = ttk.Frame(self.container)
        self.content.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Create frames for different sections
        self.dashboard_frame = ttk.Frame(self.content)
        self.financial_frame = ttk.Frame(self.content)
        self.inventory_frame = ttk.Frame(self.content)
        self.guest_house_frame = ttk.Frame(self.content)
        self.restaurant_frame = ttk.Frame(self.content)
        self.bar_frame = ttk.Frame(self.content)
        self.maintenance_frame = ttk.Frame(self.content)
        self.users_frame = ttk.Frame(self.content)
        self.reports_frame = ttk.Frame(self.content)
        self.mess_members_frame = ttk.Frame(self.content)

    def create_navigation_menu(self):
        """Create the navigation menu in the sidebar"""
        # Style for menu buttons
        style = ttk.Style()
        style.configure("Menu.TButton", font=("Arial", 12), padding=10)

        # Menu buttons
        menu_frame = ttk.Frame(self.sidebar)
        menu_frame.pack(fill=tk.X, pady=10)

        # Dashboard button
        dashboard_btn = ttk.Button(menu_frame, text="Dashboard", style="Menu.TButton",
                                  command=self.show_dashboard)
        dashboard_btn.pack(fill=tk.X, pady=2)

        # Financial button
        financial_btn = ttk.Button(menu_frame, text="Financial", style="Menu.TButton",
                                  command=self.show_financial)
        financial_btn.pack(fill=tk.X, pady=2)

        # Inventory button
        inventory_btn = ttk.Button(menu_frame, text="Inventory", style="Menu.TButton",
                                  command=self.show_inventory)
        inventory_btn.pack(fill=tk.X, pady=2)

        # Guest House button
        guest_house_btn = ttk.Button(menu_frame, text="Guest House", style="Menu.TButton",
                                    command=self.show_guest_house)
        guest_house_btn.pack(fill=tk.X, pady=2)

        # Restaurant button
        restaurant_btn = ttk.Button(menu_frame, text="Restaurant", style="Menu.TButton",
                                   command=self.show_restaurant)
        restaurant_btn.pack(fill=tk.X, pady=2)

        # Bar button
        bar_btn = ttk.Button(menu_frame, text="Bar", style="Menu.TButton",
                            command=self.show_bar)
        bar_btn.pack(fill=tk.X, pady=2)

        # Maintenance button
        maintenance_btn = ttk.Button(menu_frame, text="Maintenance", style="Menu.TButton",
                                    command=self.show_maintenance)
        maintenance_btn.pack(fill=tk.X, pady=2)

        # Mess Members button
        mess_btn = ttk.Button(menu_frame, text="Mess Members", style="Menu.TButton",
                             command=self.show_mess_members)
        mess_btn.pack(fill=tk.X, pady=2)

        # Users button
        users_btn = ttk.Button(menu_frame, text="Users", style="Menu.TButton",
                              command=self.show_users)
        users_btn.pack(fill=tk.X, pady=2)

        # Reports button
        reports_btn = ttk.Button(menu_frame, text="Reports", style="Menu.TButton",
                                command=self.show_reports)
        reports_btn.pack(fill=tk.X, pady=2)

        # Logout button at the bottom
        ttk.Separator(self.sidebar).pack(fill=tk.X, padx=10, pady=10)
        logout_btn = ttk.Button(self.sidebar, text="Logout", style="Menu.TButton",
                               command=self.logout)
        logout_btn.pack(fill=tk.X, pady=10, padx=10)

    def show_frame(self, frame):
        """Show the specified frame and hide others"""
        for f in [self.dashboard_frame, self.financial_frame, self.inventory_frame,
                 self.guest_house_frame, self.restaurant_frame, self.bar_frame,
                 self.maintenance_frame, self.mess_members_frame, self.users_frame,
                 self.reports_frame]:
            f.pack_forget()

        frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

    def show_dashboard(self):
        """Show the dashboard"""
        self.show_frame(self.dashboard_frame)

        # Clear existing widgets
        for widget in self.dashboard_frame.winfo_children():
            widget.destroy()

        # Create dashboard content
        ttk.Label(self.dashboard_frame, text="Admin Dashboard", font=("Arial", 20, "bold")).pack(pady=10)

        # Get system dashboard data
        dashboard_data = db.get_system_dashboard()

        # Create main stats frame
        stats_frame = ttk.Frame(self.dashboard_frame)
        stats_frame.pack(fill=tk.X, pady=20)

        # Configure grid
        for i in range(4):
            stats_frame.columnconfigure(i, weight=1)

        # Guest House stats
        gh_frame = ttk.LabelFrame(stats_frame, text="Guest House")
        gh_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

        occupancy = dashboard_data['guest_house']['occupancy_rate']
        occupancy_color = "#4CAF50" if occupancy > 70 else "#FFC107" if occupancy > 30 else "#F44336"

        ttk.Label(gh_frame, text=f"Occupancy Rate: {occupancy:.1f}%",
                 font=("Arial", 12, "bold"), foreground=occupancy_color).pack(pady=5)
        ttk.Label(gh_frame, text=f"Occupied Rooms: {dashboard_data['guest_house']['occupied_rooms']} / {dashboard_data['guest_house']['total_rooms']}").pack(pady=2)

        # Restaurant stats
        rest_frame = ttk.LabelFrame(stats_frame, text="Restaurant")
        rest_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")

        ttk.Label(rest_frame, text=f"Today's Orders: {dashboard_data['restaurant']['today_orders']}").pack(pady=2)
        ttk.Label(rest_frame, text=f"Today's Revenue: ₦{dashboard_data['restaurant']['today_revenue']:.2f}").pack(pady=2)

        # Inventory stats
        inv_frame = ttk.LabelFrame(stats_frame, text="Inventory")
        inv_frame.grid(row=0, column=2, padx=10, pady=10, sticky="nsew")

        ttk.Label(inv_frame, text=f"Bar Value: ₦{dashboard_data['inventory']['bar_value']:.2f}").pack(pady=2)
        ttk.Label(inv_frame, text=f"Food Value: ₦{dashboard_data['inventory']['food_value']:.2f}").pack(pady=2)

        # Maintenance stats
        maint_frame = ttk.LabelFrame(stats_frame, text="Maintenance")
        maint_frame.grid(row=0, column=3, padx=10, pady=10, sticky="nsew")

        pending = dashboard_data['maintenance']['pending_requests']
        pending_color = "#F44336" if pending > 5 else "#FFC107" if pending > 0 else "#4CAF50"

        ttk.Label(maint_frame, text=f"Pending Requests: {pending}",
                 font=("Arial", 12, "bold"), foreground=pending_color).pack(pady=5)

        # Create charts frame
        charts_frame = ttk.Frame(self.dashboard_frame)
        charts_frame.pack(fill=tk.BOTH, expand=True, pady=20)

        # Configure grid
        charts_frame.columnconfigure(0, weight=1)
        charts_frame.columnconfigure(1, weight=1)

        # Revenue chart (placeholder)
        revenue_frame = ttk.LabelFrame(charts_frame, text="Revenue (Last 7 Days)")
        revenue_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

        ttk.Label(revenue_frame, text="Revenue chart will be displayed here").pack(pady=50)

        # Occupancy chart (placeholder)
        occupancy_frame = ttk.LabelFrame(charts_frame, text="Room Occupancy (Last 30 Days)")
        occupancy_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")

        ttk.Label(occupancy_frame, text="Occupancy chart will be displayed here").pack(pady=50)

        # Recent activities
        activities_frame = ttk.LabelFrame(self.dashboard_frame, text="Recent Activities")
        activities_frame.pack(fill=tk.X, pady=10)

        # Sample activities
        activities = [
            "Room 101 booked by John Doe (10 minutes ago)",
            "New maintenance request submitted for Room 103 (30 minutes ago)",
            "Bar inventory updated by Wine Officer (1 hour ago)",
            "New user registered: Jane Smith (2 hours ago)"
        ]

        for activity in activities:
            ttk.Label(activities_frame, text=f"• {activity}", font=("Arial", 11)).pack(anchor=tk.W, padx=10, pady=3)

    def show_financial(self):
        """Show the financial page"""
        self.show_frame(self.financial_frame)

        # Clear existing widgets
        for widget in self.financial_frame.winfo_children():
            widget.destroy()

        ttk.Label(self.financial_frame, text="Financial Management", font=("Arial", 20, "bold")).pack(pady=10)

        # Create tabs for different financial sections
        notebook = ttk.Notebook(self.financial_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=10)

        # Revenue tab
        revenue_tab = ttk.Frame(notebook)
        notebook.add(revenue_tab, text="Revenue")

        # Expenses tab
        expenses_tab = ttk.Frame(notebook)
        notebook.add(expenses_tab, text="Expenses")

        # Profit/Loss tab
        profit_loss_tab = ttk.Frame(notebook)
        notebook.add(profit_loss_tab, text="Profit/Loss")

        # Invoices tab
        invoices_tab = ttk.Frame(notebook)
        notebook.add(invoices_tab, text="Invoices")

        # Setup revenue tab (placeholder)
        ttk.Label(revenue_tab, text="Revenue management coming soon...", font=("Arial", 12)).pack(pady=50)

        # Setup expenses tab (placeholder)
        ttk.Label(expenses_tab, text="Expenses management coming soon...", font=("Arial", 12)).pack(pady=50)

        # Setup profit/loss tab (placeholder)
        ttk.Label(profit_loss_tab, text="Profit/Loss reports coming soon...", font=("Arial", 12)).pack(pady=50)

        # Setup invoices tab (placeholder)
        ttk.Label(invoices_tab, text="Invoice management coming soon...", font=("Arial", 12)).pack(pady=50)

    def show_inventory(self):
        """Show the inventory page"""
        self.show_frame(self.inventory_frame)

        # Clear existing widgets
        for widget in self.inventory_frame.winfo_children():
            widget.destroy()

        ttk.Label(self.inventory_frame, text="Inventory Management", font=("Arial", 20, "bold")).pack(pady=10)

        # Create tabs for different inventory sections
        notebook = ttk.Notebook(self.inventory_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=10)

        # Bar inventory tab
        bar_tab = ttk.Frame(notebook)
        notebook.add(bar_tab, text="Bar Inventory")

        # Food inventory tab
        food_tab = ttk.Frame(notebook)
        notebook.add(food_tab, text="Food Inventory")

        # Setup bar inventory tab (placeholder)
        ttk.Label(bar_tab, text="Bar inventory management coming soon...", font=("Arial", 12)).pack(pady=50)

        # Setup food inventory tab (placeholder)
        ttk.Label(food_tab, text="Food inventory management coming soon...", font=("Arial", 12)).pack(pady=50)

    def show_guest_house(self):
        """Show the guest house management page"""
        self.show_frame(self.guest_house_frame)

        # Clear existing widgets
        for widget in self.guest_house_frame.winfo_children():
            widget.destroy()

        ttk.Label(self.guest_house_frame, text="Guest House Management", font=("Arial", 20, "bold")).pack(pady=10)

        # Create tabs for different guest house sections
        notebook = ttk.Notebook(self.guest_house_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=10)

        # Room status tab
        status_tab = ttk.Frame(notebook)
        notebook.add(status_tab, text="Room Status")

        # Bookings tab
        bookings_tab = ttk.Frame(notebook)
        notebook.add(bookings_tab, text="Bookings")

        # Financial tab
        financial_tab = ttk.Frame(notebook)
        notebook.add(financial_tab, text="Financial")

        # Setup room status tab (placeholder)
        ttk.Label(status_tab, text="Room status management coming soon...", font=("Arial", 12)).pack(pady=50)

        # Setup bookings tab (placeholder)
        ttk.Label(bookings_tab, text="Bookings management coming soon...", font=("Arial", 12)).pack(pady=50)

        # Setup financial tab (placeholder)
        ttk.Label(financial_tab, text="Guest house financial reports coming soon...", font=("Arial", 12)).pack(pady=50)

    def show_restaurant(self):
        """Show the restaurant management page"""
        self.show_frame(self.restaurant_frame)

        # Clear existing widgets
        for widget in self.restaurant_frame.winfo_children():
            widget.destroy()

        ttk.Label(self.restaurant_frame, text="Restaurant Management", font=("Arial", 20, "bold")).pack(pady=10)

        # Placeholder for now
        ttk.Label(self.restaurant_frame, text="Restaurant management coming soon...", font=("Arial", 12)).pack(pady=50)

    def show_bar(self):
        """Show the bar management page"""
        self.show_frame(self.bar_frame)

        # Clear existing widgets
        for widget in self.bar_frame.winfo_children():
            widget.destroy()

        ttk.Label(self.bar_frame, text="Bar Management", font=("Arial", 20, "bold")).pack(pady=10)

        # Placeholder for now
        ttk.Label(self.bar_frame, text="Bar management coming soon...", font=("Arial", 12)).pack(pady=50)

    def show_maintenance(self):
        """Show the maintenance management page"""
        self.show_frame(self.maintenance_frame)

        # Clear existing widgets
        for widget in self.maintenance_frame.winfo_children():
            widget.destroy()

        ttk.Label(self.maintenance_frame, text="Maintenance Management", font=("Arial", 20, "bold")).pack(pady=10)

        # Placeholder for now
        ttk.Label(self.maintenance_frame, text="Maintenance management coming soon...", font=("Arial", 12)).pack(pady=50)

    def show_users(self):
        """Show the user management page"""
        self.show_frame(self.users_frame)

        # Clear existing widgets
        for widget in self.users_frame.winfo_children():
            widget.destroy()

        ttk.Label(self.users_frame, text="User Management", font=("Arial", 20, "bold")).pack(pady=10)

        # Placeholder for now
        ttk.Label(self.users_frame, text="User management coming soon...", font=("Arial", 12)).pack(pady=50)

    def show_reports(self):
        """Show the reports page"""
        self.show_frame(self.reports_frame)

        # Clear existing widgets
        for widget in self.reports_frame.winfo_children():
            widget.destroy()

        ttk.Label(self.reports_frame, text="Reports", font=("Arial", 20, "bold")).pack(pady=10)

        # Placeholder for now
        ttk.Label(self.reports_frame, text="Reports coming soon...", font=("Arial", 12)).pack(pady=50)

    def show_mess_members(self):
        """Show the mess members management page"""
        self.show_frame(self.mess_members_frame)

        # Clear existing widgets
        for widget in self.mess_members_frame.winfo_children():
            widget.destroy()

        ttk.Label(self.mess_members_frame, text="Mess Members Management", font=("Arial", 20, "bold")).pack(pady=10)

        # Create tabs for different mess member sections
        notebook = ttk.Notebook(self.mess_members_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=10)

        # Members list tab
        members_tab = ttk.Frame(notebook)
        notebook.add(members_tab, text="Members List")

        # Honorary members tab
        honorary_tab = ttk.Frame(notebook)
        notebook.add(honorary_tab, text="Honorary Members")

        # Registration tab
        registration_tab = ttk.Frame(notebook)
        notebook.add(registration_tab, text="New Registration")

        # Setup tabs
        self.setup_members_list_tab(members_tab)
        self.setup_honorary_members_tab(honorary_tab)
        self.setup_registration_tab(registration_tab)

    def setup_members_list_tab(self, parent):
        """Setup the members list tab"""
        # Create filter frame
        filter_frame = ttk.Frame(parent, padding=10)
        filter_frame.pack(fill=tk.X, pady=10)

        # Membership type filter
        ttk.Label(filter_frame, text="Membership Type:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        type_var = tk.StringVar()
        type_combo = ttk.Combobox(filter_frame, textvariable=type_var, width=15)
        type_combo['values'] = ('All', 'Regular', 'Honorary')
        type_combo.current(0)
        type_combo.grid(row=0, column=1, padx=5, pady=5)

        # Status filter
        ttk.Label(filter_frame, text="Status:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        status_var = tk.StringVar()
        status_combo = ttk.Combobox(filter_frame, textvariable=status_var, width=15)
        status_combo['values'] = ('All', 'Active', 'Inactive', 'Expired')
        status_combo.current(0)
        status_combo.grid(row=0, column=3, padx=5, pady=5)

        # Search field
        ttk.Label(filter_frame, text="Search:").grid(row=0, column=4, padx=5, pady=5, sticky=tk.W)
        search_var = tk.StringVar()
        ttk.Entry(filter_frame, textvariable=search_var, width=20).grid(row=0, column=5, padx=5, pady=5)

        # Filter button
        ttk.Button(filter_frame, text="Filter",
                  command=lambda: self.filter_members(type_var.get(), status_var.get(), search_var.get())
                  ).grid(row=0, column=6, padx=5, pady=5)

        # Create treeview for members list
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # Create treeview with scrollbar
        columns = ("ID", "Number", "Name", "Type", "Join Date", "Status", "Balance")
        self.members_tree = ttk.Treeview(tree_frame, columns=columns, show="headings")

        # Define column headings
        for col in columns:
            self.members_tree.heading(col, text=col)
            # Set column widths
            if col in ["ID", "Status"]:
                self.members_tree.column(col, width=50)
            elif col in ["Number", "Join Date"]:
                self.members_tree.column(col, width=120)
            else:
                self.members_tree.column(col, width=100)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.members_tree.yview)
        self.members_tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.members_tree.pack(fill=tk.BOTH, expand=True)

        # Bind double-click event to view member details
        self.members_tree.bind("<Double-1>", self.view_member_details)

        # Buttons frame
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=10)

        ttk.Button(buttons_frame, text="View Details",
                  command=self.view_selected_member).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Edit Member",
                  command=self.edit_selected_member).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Deactivate Member",
                  command=self.deactivate_selected_member).pack(side=tk.LEFT, padx=5)

        # Load initial data
        self.load_members_data()

    def setup_honorary_members_tab(self, parent):
        """Setup the honorary members tab"""
        # Create filter frame
        filter_frame = ttk.Frame(parent, padding=10)
        filter_frame.pack(fill=tk.X, pady=10)

        # Verification status filter
        ttk.Label(filter_frame, text="Verification Status:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        status_var = tk.StringVar()
        status_combo = ttk.Combobox(filter_frame, textvariable=status_var, width=15)
        status_combo['values'] = ('All', 'Pending', 'Approved', 'Rejected')
        status_combo.current(0)
        status_combo.grid(row=0, column=1, padx=5, pady=5)

        # Search field
        ttk.Label(filter_frame, text="Search:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        search_var = tk.StringVar()
        ttk.Entry(filter_frame, textvariable=search_var, width=20).grid(row=0, column=3, padx=5, pady=5)

        # Filter button
        ttk.Button(filter_frame, text="Filter",
                  command=lambda: self.filter_honorary_members(status_var.get(), search_var.get())
                  ).grid(row=0, column=4, padx=5, pady=5)

        # Create treeview for honorary members
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # Create treeview with scrollbar
        columns = ("ID", "Number", "Name", "Join Date", "Verification", "Sponsors")
        self.honorary_tree = ttk.Treeview(tree_frame, columns=columns, show="headings")

        # Define column headings
        for col in columns:
            self.honorary_tree.heading(col, text=col)
            # Set column widths
            if col == "ID":
                self.honorary_tree.column(col, width=50)
            elif col in ["Number", "Join Date"]:
                self.honorary_tree.column(col, width=120)
            else:
                self.honorary_tree.column(col, width=100)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.honorary_tree.yview)
        self.honorary_tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.honorary_tree.pack(fill=tk.BOTH, expand=True)

        # Bind double-click event to view member details
        self.honorary_tree.bind("<Double-1>", self.view_honorary_member_details)

        # Buttons frame
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=10)

        ttk.Button(buttons_frame, text="View Details",
                  command=self.view_selected_honorary_member).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Verify Member",
                  command=self.verify_selected_member).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Reject Application",
                  command=self.reject_selected_member).pack(side=tk.LEFT, padx=5)

        # Load initial data
        self.load_honorary_members_data()

    def setup_registration_tab(self, parent):
        """Setup the registration tab"""
        # Create a scrollable frame
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Registration form
        form_frame = ttk.LabelFrame(scrollable_frame, text="Member Registration", padding=20)
        form_frame.pack(fill=tk.X, padx=20, pady=10)

        # Customer selection
        ttk.Label(form_frame, text="Select Customer:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.reg_customer_var = tk.StringVar()
        self.reg_customer_combo = ttk.Combobox(form_frame, textvariable=self.reg_customer_var, width=30)
        self.reg_customer_combo.grid(row=0, column=1, columnspan=2, sticky=tk.W, pady=5)

        # Or create new customer button
        ttk.Button(form_frame, text="New Customer",
                  command=self.create_new_customer).grid(row=0, column=3, pady=5)

        # Membership type
        ttk.Label(form_frame, text="Membership Type:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.reg_type_var = tk.StringVar()
        type_combo = ttk.Combobox(form_frame, textvariable=self.reg_type_var, width=20)
        type_combo['values'] = ('Regular', 'Honorary')
        type_combo.current(0)
        type_combo.grid(row=1, column=1, sticky=tk.W, pady=5)

        # Bind event to show/hide honorary fields
        type_combo.bind("<<ComboboxSelected>>", self.toggle_honorary_fields)

        # Credit limit
        ttk.Label(form_frame, text="Credit Limit (₦):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.reg_credit_var = tk.StringVar()
        self.reg_credit_var.set("0")
        ttk.Entry(form_frame, textvariable=self.reg_credit_var, width=15).grid(row=2, column=1, sticky=tk.W, pady=5)

        # Duration
        ttk.Label(form_frame, text="Duration (months):").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.reg_duration_var = tk.StringVar()
        self.reg_duration_var.set("12")
        ttk.Entry(form_frame, textvariable=self.reg_duration_var, width=15).grid(row=3, column=1, sticky=tk.W, pady=5)

        # Honorary member fields (initially hidden)
        self.honorary_frame = ttk.LabelFrame(scrollable_frame, text="Honorary Member Details", padding=20)

        # Occupation
        ttk.Label(self.honorary_frame, text="Occupation:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.reg_occupation_var = tk.StringVar()
        ttk.Entry(self.honorary_frame, textvariable=self.reg_occupation_var, width=30).grid(row=0, column=1, sticky=tk.W, pady=5)

        # Company
        ttk.Label(self.honorary_frame, text="Company:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.reg_company_var = tk.StringVar()
        ttk.Entry(self.honorary_frame, textvariable=self.reg_company_var, width=30).grid(row=1, column=1, sticky=tk.W, pady=5)

        # Position
        ttk.Label(self.honorary_frame, text="Position:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.reg_position_var = tk.StringVar()
        ttk.Entry(self.honorary_frame, textvariable=self.reg_position_var, width=30).grid(row=2, column=1, sticky=tk.W, pady=5)

        # Passport photo
        ttk.Label(self.honorary_frame, text="Passport Photo:").grid(row=3, column=0, sticky=tk.W, pady=5)
        photo_frame = ttk.Frame(self.honorary_frame)
        photo_frame.grid(row=3, column=1, sticky=tk.W, pady=5)

        self.reg_photo_path = None
        ttk.Button(photo_frame, text="Upload Photo",
                  command=self.upload_passport_photo).pack(side=tk.LEFT, padx=5)
        self.photo_label = ttk.Label(photo_frame, text="No photo selected")
        self.photo_label.pack(side=tk.LEFT, padx=5)

        # Sponsors section
        sponsors_frame = ttk.LabelFrame(self.honorary_frame, text="Sponsors (2 Required)", padding=10)
        sponsors_frame.grid(row=4, column=0, columnspan=2, sticky=tk.W+tk.E, pady=10)

        # Sponsor 1
        ttk.Label(sponsors_frame, text="Sponsor 1:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.reg_sponsor1_var = tk.StringVar()
        self.reg_sponsor1_combo = ttk.Combobox(sponsors_frame, textvariable=self.reg_sponsor1_var, width=30)
        self.reg_sponsor1_combo.grid(row=0, column=1, sticky=tk.W, pady=5)

        ttk.Label(sponsors_frame, text="Relationship:").grid(row=0, column=2, sticky=tk.W, pady=5)
        self.reg_relation1_var = tk.StringVar()
        ttk.Entry(sponsors_frame, textvariable=self.reg_relation1_var, width=15).grid(row=0, column=3, sticky=tk.W, pady=5)

        # Sponsor 2
        ttk.Label(sponsors_frame, text="Sponsor 2:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.reg_sponsor2_var = tk.StringVar()
        self.reg_sponsor2_combo = ttk.Combobox(sponsors_frame, textvariable=self.reg_sponsor2_var, width=30)
        self.reg_sponsor2_combo.grid(row=1, column=1, sticky=tk.W, pady=5)

        ttk.Label(sponsors_frame, text="Relationship:").grid(row=1, column=2, sticky=tk.W, pady=5)
        self.reg_relation2_var = tk.StringVar()
        ttk.Entry(sponsors_frame, textvariable=self.reg_relation2_var, width=15).grid(row=1, column=3, sticky=tk.W, pady=5)

        # Comments
        ttk.Label(sponsors_frame, text="Comments:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.reg_comments_var = tk.StringVar()
        ttk.Entry(sponsors_frame, textvariable=self.reg_comments_var, width=50).grid(row=2, column=1, columnspan=3, sticky=tk.W, pady=5)

        # Submit button
        ttk.Button(scrollable_frame, text="Register Member",
                  command=self.register_new_member,
                  style="Accent.TButton").pack(pady=20)

        # Load customers for dropdown
        self.load_customers_for_dropdown()

        # Hide honorary fields initially
        self.honorary_frame.pack_forget()

    def toggle_honorary_fields(self, event=None):
        """Show or hide honorary member fields based on selection"""
        if self.reg_type_var.get() == "Honorary":
            self.honorary_frame.pack(fill=tk.X, padx=20, pady=10)
        else:
            self.honorary_frame.pack_forget()

    def load_customers_for_dropdown(self):
        """Load customers for the dropdown"""
        try:
            # Get all customers
            customers = db.get_all_customers()

            # Format for dropdown: "ID - Name"
            customer_list = [f"{c['id']} - {c['name']}" for c in customers]

            # Update comboboxes
            self.reg_customer_combo['values'] = customer_list
            self.reg_sponsor1_combo['values'] = customer_list
            self.reg_sponsor2_combo['values'] = customer_list

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load customers: {str(e)}")

    def upload_passport_photo(self):
        """Upload a passport photo"""
        # This would open a file dialog to select an image
        messagebox.showinfo("Upload Photo", "Photo upload functionality coming soon...")
        self.photo_label.config(text="photo.jpg")
        self.reg_photo_path = "photo.jpg"  # Placeholder

    def create_new_customer(self):
        """Open dialog to create a new customer"""
        # This would open a dialog to create a new customer
        messagebox.showinfo("New Customer", "New customer creation functionality coming soon...")

    def register_new_member(self):
        """Register a new mess member"""
        # This would register a new mess member
        messagebox.showinfo("Register Member", "Member registration functionality coming soon...")

    def load_members_data(self):
        """Load mess members data into the treeview"""
        # Clear existing data
        for item in self.members_tree.get_children():
            self.members_tree.delete(item)

        # This would load mess members from the database
        # For now, just add some sample data
        sample_data = [
            (1, "MM-2023-0001", "John Doe", "Regular", "2023-01-15", "Active", "₦5,000"),
            (2, "HM-2023-0001", "Jane Smith", "Honorary", "2023-02-20", "Active", "₦10,000"),
            (3, "MM-2023-0002", "Bob Johnson", "Regular", "2023-03-10", "Inactive", "₦0")
        ]

        for item in sample_data:
            self.members_tree.insert("", tk.END, values=item)

    def load_honorary_members_data(self):
        """Load honorary members data into the treeview"""
        # Clear existing data
        for item in self.honorary_tree.get_children():
            self.honorary_tree.delete(item)

        # This would load honorary members from the database
        # For now, just add some sample data
        sample_data = [
            (1, "HM-2023-0001", "Jane Smith", "2023-02-20", "Approved", "2"),
            (2, "HM-2023-0002", "Alice Brown", "2023-04-05", "Pending", "2"),
            (3, "HM-2023-0003", "Tom Wilson", "2023-05-12", "Rejected", "1")
        ]

        for item in sample_data:
            self.honorary_tree.insert("", tk.END, values=item)

    def filter_members(self, membership_type, status, search_term):
        """Filter the members list"""
        # This would filter the members list based on the criteria
        messagebox.showinfo("Filter", f"Filtering members: Type={membership_type}, Status={status}, Search={search_term}")
        # Reload the data with filters
        self.load_members_data()

    def filter_honorary_members(self, status, search_term):
        """Filter the honorary members list"""
        # This would filter the honorary members list based on the criteria
        messagebox.showinfo("Filter", f"Filtering honorary members: Status={status}, Search={search_term}")
        # Reload the data with filters
        self.load_honorary_members_data()

    def view_member_details(self, event):
        """View details of the selected member (double-click)"""
        self.view_selected_member()

    def view_selected_member(self):
        """View details of the selected member"""
        selected_item = self.members_tree.selection()
        if not selected_item:
            messagebox.showwarning("Selection Required", "Please select a member to view")
            return

        # Get the member ID from the selected item
        member_id = self.members_tree.item(selected_item[0], "values")[0]

        # This would show the member details
        messagebox.showinfo("Member Details", f"Viewing details for member ID: {member_id}")

    def edit_selected_member(self):
        """Edit the selected member"""
        selected_item = self.members_tree.selection()
        if not selected_item:
            messagebox.showwarning("Selection Required", "Please select a member to edit")
            return

        # Get the member ID from the selected item
        member_id = self.members_tree.item(selected_item[0], "values")[0]

        # This would open an edit dialog
        messagebox.showinfo("Edit Member", f"Editing member ID: {member_id}")

    def deactivate_selected_member(self):
        """Deactivate the selected member"""
        selected_item = self.members_tree.selection()
        if not selected_item:
            messagebox.showwarning("Selection Required", "Please select a member to deactivate")
            return

        # Get the member ID from the selected item
        member_id = self.members_tree.item(selected_item[0], "values")[0]

        # Confirm deactivation
        if messagebox.askyesno("Confirm Deactivation", f"Are you sure you want to deactivate member ID: {member_id}?"):
            # This would deactivate the member
            messagebox.showinfo("Deactivate Member", f"Member ID: {member_id} deactivated")
            # Reload the data
            self.load_members_data()

    def view_honorary_member_details(self, event):
        """View details of the selected honorary member (double-click)"""
        self.view_selected_honorary_member()

    def view_selected_honorary_member(self):
        """View details of the selected honorary member"""
        selected_item = self.honorary_tree.selection()
        if not selected_item:
            messagebox.showwarning("Selection Required", "Please select a member to view")
            return

        # Get the member ID from the selected item
        member_id = self.honorary_tree.item(selected_item[0], "values")[0]

        # This would show the member details
        messagebox.showinfo("Honorary Member Details", f"Viewing details for member ID: {member_id}")

    def verify_selected_member(self):
        """Verify the selected honorary member"""
        selected_item = self.honorary_tree.selection()
        if not selected_item:
            messagebox.showwarning("Selection Required", "Please select a member to verify")
            return

        # Get the member ID from the selected item
        member_id = self.honorary_tree.item(selected_item[0], "values")[0]

        # Confirm verification
        if messagebox.askyesno("Confirm Verification", f"Are you sure you want to verify member ID: {member_id}?"):
            # This would verify the member
            messagebox.showinfo("Verify Member", f"Member ID: {member_id} verified")
            # Reload the data
            self.load_honorary_members_data()

    def reject_selected_member(self):
        """Reject the selected honorary member application"""
        selected_item = self.honorary_tree.selection()
        if not selected_item:
            messagebox.showwarning("Selection Required", "Please select a member to reject")
            return

        # Get the member ID from the selected item
        member_id = self.honorary_tree.item(selected_item[0], "values")[0]

        # Confirm rejection
        if messagebox.askyesno("Confirm Rejection", f"Are you sure you want to reject member ID: {member_id}?"):
            # This would reject the member
            messagebox.showinfo("Reject Member", f"Member ID: {member_id} rejected")
            # Reload the data
            self.load_honorary_members_data()

    def logout(self):
        """Log out and return to login screen"""
        if messagebox.askyesno("Logout", "Are you sure you want to logout?"):
            self.root.destroy()
            # In a real app, you would launch the login screen here
            import login
            root = tk.Tk()
            app = login.LoginApp(root)
            root.mainloop()

if __name__ == "__main__":
    # This is just for testing the admin portal directly
    root = tk.Tk()
    app = AdminPortalApp(root, 1, "CSO")
    root.mainloop()
