import tkinter as tk
from tkinter import ttk, messagebox
import datetime
import json
import threading
import time

class BarOrderingSystem:
    def __init__(self, root):
        self.root = root
        self.root.title("Hub Mess Bar - Real-time Ordering System")
        self.root.geometry("1200x800")
        self.root.configure(bg="#1a1a2e")
        
        # Initialize data
        self.current_orders = []
        self.menu_items = {
            "Beer": {
                "Star Beer": {"price": 1500, "stock": 25},
                "Heineken": {"price": 2000, "stock": 5},  # Low stock
                "Guinness": {"price": 1800, "stock": 8},
                "Trophy": {"price": 1200, "stock": 30}
            },
            "Wine": {
                "Red Wine": {"price": 8000, "stock": 12},
                "White Wine": {"price": 7500, "stock": 15},
                "Champagne": {"price": 15000, "stock": 6}
            },
            "Spirits": {
                "Whiskey": {"price": 12000, "stock": 6},
                "Vodka": {"price": 10000, "stock": 3},  # Low stock
                "Gin": {"price": 9000, "stock": 8}
            },
            "Soft Drinks": {
                "Coca Cola": {"price": 500, "stock": 50},
                "Pepsi": {"price": 500, "stock": 45},
                "Sprite": {"price": 500, "stock": 40},
                "Water": {"price": 300, "stock": 100}
            }
        }
        
        self.cart = {}
        self.total_amount = 0
        
        self.setup_ui()
        self.start_real_time_updates()
        
    def setup_ui(self):
        """Setup the bar ordering UI"""
        # Header
        header_frame = tk.Frame(self.root, bg="#16213e", height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="🍺 HUB MESS BAR - REAL-TIME ORDERING", 
                font=("Arial", 18, "bold"), fg="white", bg="#16213e").pack(pady=15)
        
        # Main content
        main_frame = tk.Frame(self.root, bg="#1a1a2e")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left side - Menu
        menu_frame = tk.Frame(main_frame, bg="#16213e", width=600)
        menu_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        menu_frame.pack_propagate(False)
        
        tk.Label(menu_frame, text="🍻 MENU", font=("Arial", 16, "bold"), 
                fg="white", bg="#16213e").pack(pady=10)
        
        # Menu notebook
        self.menu_notebook = ttk.Notebook(menu_frame)
        self.menu_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create menu tabs
        for category, items in self.menu_items.items():
            self.create_menu_tab(category, items)
        
        # Right side - Cart and Order
        right_frame = tk.Frame(main_frame, bg="#16213e", width=400)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_frame.pack_propagate(False)
        
        # Customer info
        customer_frame = ttk.LabelFrame(right_frame, text="Customer Information", padding=10)
        customer_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(customer_frame, text="Customer Type:").grid(row=0, column=0, sticky=tk.W)
        self.customer_type = tk.StringVar(value="Bar Hall")
        customer_combo = ttk.Combobox(customer_frame, textvariable=self.customer_type, width=15)
        customer_combo['values'] = ('Bar Hall', 'Guest House', 'Officer', 'HM Member')
        customer_combo.grid(row=0, column=1, padx=5)
        
        tk.Label(customer_frame, text="Table/Room:").grid(row=1, column=0, sticky=tk.W)
        self.table_number = tk.StringVar()
        tk.Entry(customer_frame, textvariable=self.table_number, width=15).grid(row=1, column=1, padx=5, pady=5)
        
        # Cart
        cart_frame = ttk.LabelFrame(right_frame, text="🛒 Your Order", padding=10)
        cart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Cart items
        self.cart_tree = ttk.Treeview(cart_frame, columns=("Item", "Qty", "Price", "Total"), show="headings", height=8)
        self.cart_tree.heading("Item", text="Item")
        self.cart_tree.heading("Qty", text="Qty")
        self.cart_tree.heading("Price", text="Price")
        self.cart_tree.heading("Total", text="Total")
        
        self.cart_tree.column("Item", width=120)
        self.cart_tree.column("Qty", width=50)
        self.cart_tree.column("Price", width=70)
        self.cart_tree.column("Total", width=70)
        
        self.cart_tree.pack(fill=tk.BOTH, expand=True)
        
        # Total
        total_frame = tk.Frame(cart_frame)
        total_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(total_frame, text="TOTAL:", font=("Arial", 12, "bold")).pack(side=tk.LEFT)
        self.total_label = tk.Label(total_frame, text="₦0", font=("Arial", 12, "bold"), fg="green")
        self.total_label.pack(side=tk.RIGHT)
        
        # Order buttons
        order_buttons_frame = tk.Frame(right_frame)
        order_buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Button(order_buttons_frame, text="🗑️ Clear Cart", bg="#dc2626", fg="white",
                 command=self.clear_cart).pack(fill=tk.X, pady=2)
        tk.Button(order_buttons_frame, text="💳 Order & Pay", bg="#10b981", fg="white",
                 font=("Arial", 11, "bold"), command=self.process_order).pack(fill=tk.X, pady=2)
        
        # Live orders status
        status_frame = ttk.LabelFrame(right_frame, text="📊 Live Orders", padding=10)
        status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.status_text = tk.Text(status_frame, height=6, width=30, font=("Arial", 9))
        self.status_text.pack(fill=tk.X)
        
    def create_menu_tab(self, category, items):
        """Create a menu tab for each category"""
        tab_frame = ttk.Frame(self.menu_notebook)
        self.menu_notebook.add(tab_frame, text=category)
        
        # Create scrollable frame
        canvas = tk.Canvas(tab_frame, bg="white")
        scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="white")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Menu items
        row = 0
        for item_name, item_data in items.items():
            item_frame = tk.Frame(scrollable_frame, relief=tk.RAISED, borderwidth=1, bg="white")
            item_frame.grid(row=row, column=0, padx=5, pady=5, sticky="ew")
            
            # Item info
            info_frame = tk.Frame(item_frame, bg="white")
            info_frame.pack(fill=tk.X, padx=10, pady=5)
            
            tk.Label(info_frame, text=item_name, font=("Arial", 12, "bold"), bg="white").pack(anchor=tk.W)
            tk.Label(info_frame, text=f"₦{item_data['price']:,}", font=("Arial", 11), fg="green", bg="white").pack(anchor=tk.W)
            
            # Stock status
            stock = item_data['stock']
            if stock <= 5:
                stock_color = "red"
                stock_text = f"⚠️ Low Stock: {stock} left"
            elif stock <= 10:
                stock_color = "orange"
                stock_text = f"⚠️ {stock} left"
            else:
                stock_color = "green"
                stock_text = f"✅ In Stock: {stock}"
                
            tk.Label(info_frame, text=stock_text, font=("Arial", 9), fg=stock_color, bg="white").pack(anchor=tk.W)
            
            # Add to cart button
            if stock > 0:
                tk.Button(info_frame, text="➕ Add to Cart", bg="#3b82f6", fg="white",
                         command=lambda name=item_name, price=item_data['price']: self.add_to_cart(name, price)).pack(anchor=tk.E, pady=5)
            else:
                tk.Label(info_frame, text="❌ Out of Stock", fg="red", bg="white").pack(anchor=tk.E, pady=5)
            
            row += 1
        
        scrollable_frame.columnconfigure(0, weight=1)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def add_to_cart(self, item_name, price):
        """Add item to cart"""
        if item_name in self.cart:
            self.cart[item_name]['quantity'] += 1
        else:
            self.cart[item_name] = {'price': price, 'quantity': 1}
        
        self.update_cart_display()
        
    def update_cart_display(self):
        """Update cart display"""
        # Clear cart tree
        for item in self.cart_tree.get_children():
            self.cart_tree.delete(item)
        
        # Add items to cart tree
        total = 0
        for item_name, item_data in self.cart.items():
            quantity = item_data['quantity']
            price = item_data['price']
            item_total = quantity * price
            total += item_total
            
            self.cart_tree.insert("", tk.END, values=(item_name, quantity, f"₦{price:,}", f"₦{item_total:,}"))
        
        self.total_amount = total
        self.total_label.config(text=f"₦{total:,}")
        
    def clear_cart(self):
        """Clear the cart"""
        self.cart.clear()
        self.update_cart_display()
        
    def process_order(self):
        """Process the order and payment"""
        if not self.cart:
            messagebox.showwarning("Empty Cart", "Please add items to your cart first!")
            return
            
        if not self.table_number.get():
            messagebox.showwarning("Missing Info", "Please enter table/room number!")
            return
        
        # Create order
        order = {
            'order_id': f"ORD-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}",
            'customer_type': self.customer_type.get(),
            'table': self.table_number.get(),
            'items': self.cart.copy(),
            'total': self.total_amount,
            'timestamp': datetime.datetime.now().strftime('%H:%M:%S'),
            'status': 'Payment Pending'
        }
        
        # Show payment dialog
        self.show_payment_dialog(order)
        
    def show_payment_dialog(self, order):
        """Show payment dialog"""
        payment_window = tk.Toplevel(self.root)
        payment_window.title("Payment")
        payment_window.geometry("500x400")
        payment_window.transient(self.root)
        payment_window.grab_set()
        payment_window.configure(bg="white")
        
        # Header
        tk.Label(payment_window, text="💳 Payment", font=("Arial", 16, "bold"), bg="white").pack(pady=10)
        
        # Order summary
        summary_frame = ttk.LabelFrame(payment_window, text="Order Summary", padding=15)
        summary_frame.pack(fill=tk.X, padx=20, pady=10)
        
        summary_text = f"""Order ID: {order['order_id']}
Customer: {order['customer_type']}
Table/Room: {order['table']}
Time: {order['timestamp']}

Items:"""
        
        for item_name, item_data in order['items'].items():
            summary_text += f"\n• {item_name} x{item_data['quantity']} = ₦{item_data['quantity'] * item_data['price']:,}"
        
        summary_text += f"\n\nTOTAL: ₦{order['total']:,}"
        
        tk.Label(summary_frame, text=summary_text, justify=tk.LEFT, font=("Arial", 10), bg="white").pack(anchor=tk.W)
        
        # Payment methods
        payment_frame = ttk.LabelFrame(payment_window, text="Payment Method", padding=15)
        payment_frame.pack(fill=tk.X, padx=20, pady=10)
        
        payment_method = tk.StringVar(value="Card")
        
        tk.Radiobutton(payment_frame, text="💳 Debit/Credit Card", variable=payment_method, value="Card", bg="white").pack(anchor=tk.W)
        tk.Radiobutton(payment_frame, text="📱 Bank Transfer", variable=payment_method, value="Transfer", bg="white").pack(anchor=tk.W)
        tk.Radiobutton(payment_frame, text="💰 Cash (Pay at Bar)", variable=payment_method, value="Cash", bg="white").pack(anchor=tk.W)
        
        # Payment buttons
        button_frame = tk.Frame(payment_window, bg="white")
        button_frame.pack(fill=tk.X, padx=20, pady=20)
        
        tk.Button(button_frame, text="💳 Pay Now", bg="#10b981", fg="white", font=("Arial", 12, "bold"),
                 command=lambda: self.complete_payment(order, payment_method.get(), payment_window)).pack(side=tk.RIGHT, padx=5)
        tk.Button(button_frame, text="Cancel", bg="#dc2626", fg="white",
                 command=payment_window.destroy).pack(side=tk.RIGHT, padx=5)
        
    def complete_payment(self, order, payment_method, payment_window):
        """Complete payment and send order to bar"""
        payment_window.destroy()
        
        # Update order status
        order['status'] = 'Paid - Preparing'
        order['payment_method'] = payment_method
        
        # Add to current orders
        self.current_orders.append(order)
        
        # Update inventory
        self.update_inventory(order['items'])
        
        # Show success message
        messagebox.showinfo("Payment Successful", 
                           f"Payment of ₦{order['total']:,} successful!\n\n"
                           f"Order ID: {order['order_id']}\n"
                           f"Your order has been sent to the bar.\n"
                           f"Please wait for confirmation.")
        
        # Clear cart
        self.clear_cart()
        
        # Simulate barman notification
        self.simulate_barman_notification(order)
        
        # Update CSO dashboard (simulate)
        self.update_cso_dashboard(order)
        
    def update_inventory(self, items):
        """Update inventory after order"""
        for item_name, item_data in items.items():
            quantity = item_data['quantity']
            
            # Find item in menu and reduce stock
            for category, menu_items in self.menu_items.items():
                if item_name in menu_items:
                    self.menu_items[category][item_name]['stock'] -= quantity
                    break
        
        # Refresh menu display
        self.refresh_menu_display()
        
    def refresh_menu_display(self):
        """Refresh menu display with updated stock"""
        # Clear and recreate menu tabs
        for tab in self.menu_notebook.tabs():
            self.menu_notebook.forget(tab)
        
        for category, items in self.menu_items.items():
            self.create_menu_tab(category, items)
            
    def simulate_barman_notification(self, order):
        """Simulate barman receiving order notification"""
        # In production, this would be a real notification to barman's device
        def barman_confirms():
            time.sleep(5)  # Simulate preparation time
            
            # Update order status
            for i, current_order in enumerate(self.current_orders):
                if current_order['order_id'] == order['order_id']:
                    self.current_orders[i]['status'] = 'Ready for Pickup'
                    break
            
            # Notify customer
            self.root.after(0, lambda: messagebox.showinfo("Order Ready", 
                                                           f"Order {order['order_id']} is ready for pickup!\n"
                                                           f"Table/Room: {order['table']}"))
        
        # Start barman confirmation in background
        threading.Thread(target=barman_confirms, daemon=True).start()
        
    def update_cso_dashboard(self, order):
        """Update CSO dashboard with new sale (simulate)"""
        # In production, this would update the actual CSO dashboard
        print(f"CSO Dashboard Update: New sale ₦{order['total']:,} from {order['customer_type']}")
        
    def start_real_time_updates(self):
        """Start real-time updates for live orders"""
        def update_status():
            status_text = "LIVE ORDERS STATUS:\n\n"
            
            if not self.current_orders:
                status_text += "No active orders"
            else:
                for order in self.current_orders[-5:]:  # Show last 5 orders
                    status_text += f"🔸 {order['order_id'][-6:]}\n"
                    status_text += f"   {order['customer_type']} - Table {order['table']}\n"
                    status_text += f"   ₦{order['total']:,} - {order['status']}\n\n"
            
            self.status_text.delete(1.0, tk.END)
            self.status_text.insert(1.0, status_text)
            
            # Schedule next update
            self.root.after(2000, update_status)  # Update every 2 seconds
        
        # Start updates
        update_status()

if __name__ == "__main__":
    root = tk.Tk()
    app = BarOrderingSystem(root)
    root.mainloop()
