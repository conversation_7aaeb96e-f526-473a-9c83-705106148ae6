import sqlite3
import datetime
import qrcode
from io import BytesIO
import base64


class Database:
    def __init__(self, db):
        self.conn = sqlite3.connect(db)
        self.cur = self.conn.cursor()

        # Create the inventory table (enhanced from parts table)
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS inventory (
                id INTEGER PRIMARY KEY,
                item_name TEXT NOT NULL,
                category TEXT DEFAULT 'General',
                supplier TEXT,
                price REAL NOT NULL,
                quantity INTEGER DEFAULT 1,
                expiration_date TEXT,
                purchase_date TEXT DEFAULT CURRENT_DATE,
                qr_code TEXT,
                notes TEXT
            )
        """)

        # Create customers table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                rank TEXT,
                department TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                registration_date TEXT DEFAULT CURRENT_DATE
            )
        """)

        # Create restaurant_menu table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS restaurant_menu (
                id INTEGER PRIMARY KEY,
                restaurant_name TEXT NOT NULL,
                item_name TEXT NOT NULL,
                category TEXT,
                price REAL NOT NULL,
                description TEXT,
                availability BOOLEAN DEFAULT 1
            )
        """)

        # Create restaurant_orders table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS restaurant_orders (
                id INTEGER PRIMARY KEY,
                customer_id INTEGER,
                restaurant_name TEXT NOT NULL,
                order_date TEXT DEFAULT CURRENT_TIMESTAMP,
                total_amount REAL,
                status TEXT DEFAULT 'Pending',
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        """)

        # Create order_items table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS order_items (
                id INTEGER PRIMARY KEY,
                order_id INTEGER,
                menu_item_id INTEGER,
                quantity INTEGER DEFAULT 1,
                price REAL,
                FOREIGN KEY (order_id) REFERENCES restaurant_orders (id),
                FOREIGN KEY (menu_item_id) REFERENCES restaurant_menu (id)
            )
        """)

        # Create gym_memberships table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS gym_memberships (
                id INTEGER PRIMARY KEY,
                customer_id INTEGER,
                membership_type TEXT,
                start_date TEXT,
                end_date TEXT,
                status TEXT DEFAULT 'Active',
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        """)

        # Create gym_attendance table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS gym_attendance (
                id INTEGER PRIMARY KEY,
                membership_id INTEGER,
                check_in_time TEXT,
                check_out_time TEXT,
                FOREIGN KEY (membership_id) REFERENCES gym_memberships (id)
            )
        """)

        # Create guest_house table (enhanced with hotel management features)
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS guest_house (
                id INTEGER PRIMARY KEY,
                room_number TEXT NOT NULL,
                room_type TEXT,
                capacity INTEGER,
                beds INTEGER DEFAULT 1,
                ac_available TEXT DEFAULT 'Yes',
                tv_available TEXT DEFAULT 'Yes',
                wifi_available TEXT DEFAULT 'Yes',
                price_per_night REAL,
                status TEXT DEFAULT 'Available',
                features TEXT,
                image TEXT,
                floor_number INTEGER DEFAULT 1,
                room_size TEXT,
                bathroom_type TEXT DEFAULT 'Private',
                balcony TEXT DEFAULT 'No',
                last_maintenance_date TEXT,
                assigned_staff_id INTEGER,
                FOREIGN KEY (assigned_staff_id) REFERENCES users (id)
            )
        """)

        # Create guest_house_bookings table (enhanced with hotel management features)
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS guest_house_bookings (
                id INTEGER PRIMARY KEY,
                customer_id INTEGER,
                room_id INTEGER,
                check_in_date TEXT,
                check_in_time TEXT,
                check_out_date TEXT,
                check_out_time TEXT,
                total_amount REAL,
                advance_payment REAL DEFAULT 0,
                balance_amount REAL DEFAULT 0,
                id_card TEXT,
                photo TEXT,
                meal_plan TEXT,
                has_companions BOOLEAN DEFAULT 0,
                number_of_adults INTEGER DEFAULT 1,
                number_of_children INTEGER DEFAULT 0,
                special_requests TEXT,
                guest_address TEXT,
                emergency_contact TEXT,
                payment_method TEXT DEFAULT 'Cash',
                payment_status TEXT DEFAULT 'Pending',
                booking_source TEXT DEFAULT 'Walk-in',
                status TEXT DEFAULT 'Confirmed',
                created_date TEXT DEFAULT CURRENT_TIMESTAMP,
                modified_date TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (room_id) REFERENCES guest_house (id)
            )
        """)

        # Create guest_companions table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS guest_companions (
                id INTEGER PRIMARY KEY,
                booking_id INTEGER,
                name TEXT NOT NULL,
                relationship TEXT,
                id_card TEXT,
                phone TEXT,
                FOREIGN KEY (booking_id) REFERENCES guest_house_bookings (id)
            )
        """)

        # Create guest_house_orders table for meals, drinks, etc.
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS guest_house_orders (
                id INTEGER PRIMARY KEY,
                booking_id INTEGER,
                order_type TEXT,
                order_date TEXT,
                items TEXT,
                total_amount REAL,
                FOREIGN KEY (booking_id) REFERENCES guest_house_bookings (id)
            )
        """)

        # Create invoices table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY,
                customer_id INTEGER,
                invoice_date TEXT DEFAULT CURRENT_DATE,
                total_amount REAL,
                payment_status TEXT DEFAULT 'Pending',
                module TEXT,
                reference_id INTEGER,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        """)

        # Create invoice_items table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS invoice_items (
                id INTEGER PRIMARY KEY,
                invoice_id INTEGER,
                item_description TEXT,
                quantity INTEGER,
                unit_price REAL,
                total_price REAL,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id)
            )
        """)

        # Create bar_inventory table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS bar_inventory (
                id INTEGER PRIMARY KEY,
                item_name TEXT NOT NULL,
                category TEXT,
                supplier TEXT,
                price REAL NOT NULL,
                cost_price REAL NOT NULL,
                quantity INTEGER DEFAULT 1,
                expiration_date TEXT,
                purchase_date TEXT DEFAULT CURRENT_DATE,
                qr_code TEXT,
                notes TEXT
            )
        """)

        # Create food_inventory table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS food_inventory (
                id INTEGER PRIMARY KEY,
                item_name TEXT NOT NULL,
                category TEXT,
                supplier TEXT,
                price REAL NOT NULL,
                cost_price REAL NOT NULL,
                quantity INTEGER DEFAULT 1,
                expiration_date TEXT,
                purchase_date TEXT DEFAULT CURRENT_DATE,
                qr_code TEXT,
                notes TEXT
            )
        """)

        # Create financial_records table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS financial_records (
                id INTEGER PRIMARY KEY,
                module TEXT NOT NULL,
                record_date TEXT DEFAULT CURRENT_DATE,
                record_type TEXT NOT NULL,
                amount REAL NOT NULL,
                description TEXT,
                reference_id INTEGER
            )
        """)

        # Create profit_loss_records table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS profit_loss_records (
                id INTEGER PRIMARY KEY,
                module TEXT NOT NULL,
                period TEXT NOT NULL,
                start_date TEXT,
                end_date TEXT,
                revenue REAL DEFAULT 0,
                expenses REAL DEFAULT 0,
                profit REAL DEFAULT 0,
                notes TEXT
            )
        """)

        # Create users table with role-based access
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                last_login TEXT,
                status TEXT DEFAULT 'Active'
            )
        """)

        # Create user_permissions table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS user_permissions (
                id INTEGER PRIMARY KEY,
                user_id INTEGER,
                module TEXT NOT NULL,
                can_view BOOLEAN DEFAULT 0,
                can_add BOOLEAN DEFAULT 0,
                can_edit BOOLEAN DEFAULT 0,
                can_delete BOOLEAN DEFAULT 0,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)

        # Create maintenance_requests table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS maintenance_requests (
                id INTEGER PRIMARY KEY,
                requester_id INTEGER,
                request_date TEXT DEFAULT CURRENT_DATE,
                request_time TEXT DEFAULT CURRENT_TIME,
                location TEXT NOT NULL,
                issue_type TEXT NOT NULL,
                description TEXT,
                priority TEXT DEFAULT 'Medium',
                status TEXT DEFAULT 'Pending',
                assigned_to INTEGER,
                completion_date TEXT,
                notes TEXT,
                FOREIGN KEY (requester_id) REFERENCES users (id),
                FOREIGN KEY (assigned_to) REFERENCES users (id)
            )
        """)

        # Create mess_members table with enhanced fields for honorary members
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS mess_members (
                id INTEGER PRIMARY KEY,
                customer_id INTEGER,
                membership_number TEXT UNIQUE,
                membership_type TEXT,
                join_date TEXT DEFAULT CURRENT_DATE,
                expiry_date TEXT,
                status TEXT DEFAULT 'Active',
                credit_limit REAL DEFAULT 0,
                current_balance REAL DEFAULT 0,
                occupation TEXT,
                company TEXT,
                position TEXT,
                passport_photo BLOB,
                verification_status TEXT DEFAULT 'Pending',
                verification_date TEXT,
                verified_by INTEGER,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (verified_by) REFERENCES users (id)
            )
        """)

        # Create mess_member_sponsors table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS mess_member_sponsors (
                id INTEGER PRIMARY KEY,
                member_id INTEGER,
                sponsor_id INTEGER,
                relationship TEXT,
                sponsor_date TEXT DEFAULT CURRENT_DATE,
                comments TEXT,
                FOREIGN KEY (member_id) REFERENCES mess_members (id),
                FOREIGN KEY (sponsor_id) REFERENCES customers (id)
            )
        """)

        # Create news_feed table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS news_feed (
                id INTEGER PRIMARY KEY,
                category TEXT NOT NULL,
                title TEXT NOT NULL,
                content TEXT,
                publish_date TEXT DEFAULT CURRENT_DATE,
                publish_time TEXT DEFAULT CURRENT_TIME,
                expiry_date TEXT,
                status TEXT DEFAULT 'Active',
                image TEXT
            )
        """)

        # Create football_updates table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS football_updates (
                id INTEGER PRIMARY KEY,
                match_date TEXT,
                team_home TEXT NOT NULL,
                team_away TEXT NOT NULL,
                score_home INTEGER,
                score_away INTEGER,
                league TEXT,
                status TEXT DEFAULT 'Upcoming',
                highlights TEXT,
                notes TEXT
            )
        """)

        # Create payment_transactions table (enhanced payment tracking)
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS payment_transactions (
                id INTEGER PRIMARY KEY,
                booking_id INTEGER,
                customer_id INTEGER,
                transaction_date TEXT DEFAULT CURRENT_TIMESTAMP,
                amount REAL NOT NULL,
                payment_method TEXT NOT NULL,
                payment_status TEXT DEFAULT 'Completed',
                transaction_reference TEXT,
                payment_gateway TEXT,
                gateway_transaction_id TEXT,
                notes TEXT,
                created_by INTEGER,
                FOREIGN KEY (booking_id) REFERENCES guest_house_bookings (id),
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        """)

        # Create staff_assignments table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS staff_assignments (
                id INTEGER PRIMARY KEY,
                staff_id INTEGER,
                room_id INTEGER,
                assignment_date TEXT DEFAULT CURRENT_DATE,
                assignment_type TEXT,
                shift_start TEXT,
                shift_end TEXT,
                status TEXT DEFAULT 'Active',
                notes TEXT,
                FOREIGN KEY (staff_id) REFERENCES users (id),
                FOREIGN KEY (room_id) REFERENCES guest_house (id)
            )
        """)

        # Create room_maintenance_log table
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS room_maintenance_log (
                id INTEGER PRIMARY KEY,
                room_id INTEGER,
                maintenance_type TEXT NOT NULL,
                description TEXT,
                scheduled_date TEXT,
                completed_date TEXT,
                assigned_staff INTEGER,
                cost REAL DEFAULT 0,
                status TEXT DEFAULT 'Scheduled',
                priority TEXT DEFAULT 'Medium',
                notes TEXT,
                FOREIGN KEY (room_id) REFERENCES guest_house (id),
                FOREIGN KEY (assigned_staff) REFERENCES users (id)
            )
        """)

        # Create wifi_access_codes table for QR-based WiFi access
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS wifi_access_codes (
                id INTEGER PRIMARY KEY,
                customer_id INTEGER,
                booking_id INTEGER,
                order_id INTEGER,
                access_code TEXT UNIQUE NOT NULL,
                qr_code_data TEXT,
                wifi_password TEXT,
                created_date TEXT DEFAULT CURRENT_TIMESTAMP,
                expiry_date TEXT,
                status TEXT DEFAULT 'Active',
                device_limit INTEGER DEFAULT 2,
                usage_count INTEGER DEFAULT 0,
                access_type TEXT DEFAULT 'Guest',
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (booking_id) REFERENCES guest_house_bookings (id)
            )
        """)

        # Create app_downloads table to track mobile app usage
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS app_downloads (
                id INTEGER PRIMARY KEY,
                customer_id INTEGER,
                download_date TEXT DEFAULT CURRENT_TIMESTAMP,
                device_type TEXT,
                app_version TEXT,
                platform TEXT,
                referral_source TEXT DEFAULT 'QR_Code',
                first_action TEXT,
                status TEXT DEFAULT 'Active',
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        """)

        # Create customer_rewards table for loyalty program
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS customer_rewards (
                id INTEGER PRIMARY KEY,
                customer_id INTEGER,
                points_earned INTEGER DEFAULT 0,
                points_redeemed INTEGER DEFAULT 0,
                current_balance INTEGER DEFAULT 0,
                tier_level TEXT DEFAULT 'Bronze',
                last_activity_date TEXT DEFAULT CURRENT_TIMESTAMP,
                wifi_access_count INTEGER DEFAULT 0,
                app_usage_count INTEGER DEFAULT 0,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        """)

        # Create digital_services table for QR-based services
        self.cur.execute("""
            CREATE TABLE IF NOT EXISTS digital_services (
                id INTEGER PRIMARY KEY,
                service_name TEXT NOT NULL,
                service_type TEXT NOT NULL,
                qr_code_template TEXT,
                access_duration INTEGER DEFAULT 24,
                price REAL DEFAULT 0,
                description TEXT,
                status TEXT DEFAULT 'Active',
                created_date TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)

        self.conn.commit()

        # Initialize default admin user if no users exist
        self.cur.execute("SELECT COUNT(*) FROM users")
        user_count = self.cur.fetchone()[0]

        if user_count == 0:
            # Create default CSO admin user (password should be changed immediately)
            self.add_user("admin", "admin123", "Chief Staff Officer / President Mess Committee", "CSO", "<EMAIL>", "08000000000")

            # Create default Wine Officer
            self.add_user("wineofficer", "wine123", "Wine Officer", "Wine Officer", "<EMAIL>", "08000000001")

            # Create default Property Officer
            self.add_user("property", "property123", "Property Officer", "Property Officer", "<EMAIL>", "08000000002")

    # General methods
    def generate_qr_code(self, data):
        """Generate QR code for an item and return as base64 string"""
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(data)
        qr.make(fit=True)
        img = qr.make_image(fill_color="black", back_color="white")

        # Convert image to base64 string
        buffer = BytesIO()
        img.save(buffer, format="PNG")
        return base64.b64encode(buffer.getvalue()).decode()

    # Inventory methods
    def fetch_inventory(self, category=None):
        """Fetch inventory items, optionally filtered by category"""
        if category:
            self.cur.execute("SELECT * FROM inventory WHERE category = ?", (category,))
        else:
            self.cur.execute("SELECT * FROM inventory")
        return self.cur.fetchall()

    def insert_inventory(self, item_name, category, supplier, price, quantity=1, expiration_date=None, notes=None):
        """Insert a new item into inventory with QR code generation"""
        # Generate a unique identifier for the QR code
        current_time = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        qr_data = f"ITEM:{item_name}|ID:{current_time}|PRICE:{price}"
        qr_code = self.generate_qr_code(qr_data)

        purchase_date = datetime.datetime.now().strftime("%Y-%m-%d")

        self.cur.execute("""
            INSERT INTO inventory
            (item_name, category, supplier, price, quantity, expiration_date, purchase_date, qr_code, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (item_name, category, supplier, price, quantity, expiration_date, purchase_date, qr_code, notes))
        self.conn.commit()
        return self.cur.lastrowid

    def update_inventory(self, id, item_name, category, supplier, price, quantity, expiration_date=None, notes=None):
        """Update an existing inventory item"""
        self.cur.execute("""
            UPDATE inventory
            SET item_name = ?, category = ?, supplier = ?, price = ?,
                quantity = ?, expiration_date = ?, notes = ?
            WHERE id = ?
        """, (item_name, category, supplier, price, quantity, expiration_date, notes, id))
        self.conn.commit()

    def remove_inventory(self, id):
        """Remove an item from inventory"""
        self.cur.execute("DELETE FROM inventory WHERE id = ?", (id,))
        self.conn.commit()

    def update_inventory_quantity(self, id, quantity_change):
        """Update inventory quantity (positive for additions, negative for reductions)"""
        self.cur.execute("SELECT quantity FROM inventory WHERE id = ?", (id,))
        current_quantity = self.cur.fetchone()[0]
        new_quantity = current_quantity + quantity_change

        if new_quantity < 0:
            return False  # Cannot have negative inventory

        self.cur.execute("UPDATE inventory SET quantity = ? WHERE id = ?", (new_quantity, id))
        self.conn.commit()
        return True

    # Customer methods
    def add_customer(self, name, rank=None, department=None, phone=None, email=None, address=None):
        """Add a new customer"""
        registration_date = datetime.datetime.now().strftime("%Y-%m-%d")
        self.cur.execute("""
            INSERT INTO customers (name, rank, department, phone, email, address, registration_date)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (name, rank, department, phone, email, address, registration_date))
        self.conn.commit()
        return self.cur.lastrowid

    def get_customer(self, id):
        """Get customer by ID"""
        self.cur.execute("SELECT * FROM customers WHERE id = ?", (id,))
        return self.cur.fetchone()

    def search_customers(self, search_term):
        """Search customers by name, rank, or department"""
        search_pattern = f"%{search_term}%"
        self.cur.execute("""
            SELECT * FROM customers
            WHERE name LIKE ? OR rank LIKE ? OR department LIKE ?
        """, (search_pattern, search_pattern, search_pattern))
        return self.cur.fetchall()

    # Restaurant methods
    def add_menu_item(self, restaurant_name, item_name, category, price, description=None, availability=True):
        """Add a new menu item"""
        self.cur.execute("""
            INSERT INTO restaurant_menu
            (restaurant_name, item_name, category, price, description, availability)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (restaurant_name, item_name, category, price, description, availability))
        self.conn.commit()
        return self.cur.lastrowid

    def get_menu(self, restaurant_name=None, category=None):
        """Get menu items, optionally filtered by restaurant and/or category"""
        if restaurant_name and category:
            self.cur.execute("""
                SELECT * FROM restaurant_menu
                WHERE restaurant_name = ? AND category = ? AND availability = 1
            """, (restaurant_name, category))
        elif restaurant_name:
            self.cur.execute("""
                SELECT * FROM restaurant_menu
                WHERE restaurant_name = ? AND availability = 1
            """, (restaurant_name,))
        elif category:
            self.cur.execute("""
                SELECT * FROM restaurant_menu
                WHERE category = ? AND availability = 1
            """, (category,))
        else:
            self.cur.execute("SELECT * FROM restaurant_menu WHERE availability = 1")
        return self.cur.fetchall()

    def create_restaurant_order(self, customer_id, restaurant_name):
        """Create a new restaurant order"""
        self.cur.execute("""
            INSERT INTO restaurant_orders (customer_id, restaurant_name)
            VALUES (?, ?)
        """, (customer_id, restaurant_name))
        self.conn.commit()
        return self.cur.lastrowid

    def add_order_item(self, order_id, menu_item_id, quantity):
        """Add an item to a restaurant order"""
        # Get the price of the menu item
        self.cur.execute("SELECT price FROM restaurant_menu WHERE id = ?", (menu_item_id,))
        price = self.cur.fetchone()[0]

        self.cur.execute("""
            INSERT INTO order_items (order_id, menu_item_id, quantity, price)
            VALUES (?, ?, ?, ?)
        """, (order_id, menu_item_id, quantity, price))
        self.conn.commit()

        # Update the total amount in the order
        self.update_order_total(order_id)
        return self.cur.lastrowid

    def update_order_total(self, order_id):
        """Update the total amount of an order based on its items"""
        self.cur.execute("""
            SELECT SUM(quantity * price) FROM order_items WHERE order_id = ?
        """, (order_id,))
        total = self.cur.fetchone()[0] or 0

        self.cur.execute("""
            UPDATE restaurant_orders SET total_amount = ? WHERE id = ?
        """, (total, order_id))
        self.conn.commit()
        return total

    def complete_order(self, order_id, status="Completed"):
        """Mark an order as completed and generate invoice"""
        self.cur.execute("""
            UPDATE restaurant_orders SET status = ? WHERE id = ?
        """, (status, order_id))
        self.conn.commit()

        # Get order details for invoice
        self.cur.execute("""
            SELECT customer_id, total_amount FROM restaurant_orders WHERE id = ?
        """, (order_id,))
        customer_id, total_amount = self.cur.fetchone()

        # Create invoice
        return self.create_invoice(customer_id, total_amount, "Restaurant", order_id)

    # Gym membership methods
    def create_gym_membership(self, customer_id, membership_type, duration_months=1):
        """Create a new gym membership"""
        start_date = datetime.datetime.now()
        end_date = start_date + datetime.timedelta(days=30*duration_months)

        self.cur.execute("""
            INSERT INTO gym_memberships
            (customer_id, membership_type, start_date, end_date)
            VALUES (?, ?, ?, ?)
        """, (customer_id, membership_type, start_date.strftime("%Y-%m-%d"),
              end_date.strftime("%Y-%m-%d")))
        self.conn.commit()
        return self.cur.lastrowid

    def record_gym_check_in(self, membership_id):
        """Record a gym check-in"""
        check_in_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        self.cur.execute("""
            INSERT INTO gym_attendance (membership_id, check_in_time)
            VALUES (?, ?)
        """, (membership_id, check_in_time))
        self.conn.commit()
        return self.cur.lastrowid

    def record_gym_check_out(self, attendance_id):
        """Record a gym check-out"""
        check_out_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        self.cur.execute("""
            UPDATE gym_attendance SET check_out_time = ? WHERE id = ?
        """, (check_out_time, attendance_id))
        self.conn.commit()

    def get_active_memberships(self, customer_id=None):
        """Get active gym memberships, optionally filtered by customer"""
        today = datetime.datetime.now().strftime("%Y-%m-%d")

        if customer_id:
            self.cur.execute("""
                SELECT * FROM gym_memberships
                WHERE customer_id = ? AND end_date >= ? AND status = 'Active'
            """, (customer_id, today))
        else:
            self.cur.execute("""
                SELECT * FROM gym_memberships
                WHERE end_date >= ? AND status = 'Active'
            """, (today,))
        return self.cur.fetchall()

    # Guest house methods
    def add_guest_house_room(self, room_number, room_type, capacity, price_per_night):
        """Add a new guest house room"""
        self.cur.execute("""
            INSERT INTO guest_house (room_number, room_type, capacity, price_per_night)
            VALUES (?, ?, ?, ?)
        """, (room_number, room_type, capacity, price_per_night))
        self.conn.commit()
        return self.cur.lastrowid

    def get_available_rooms(self, check_in_date, check_out_date, capacity=None, beds=None, ac_required=None, tv_required=None, wifi_required=None, max_price=None):
        """Get available guest house rooms for a date range with enhanced filtering"""
        base_query = """
            SELECT * FROM guest_house
            WHERE id NOT IN (
                SELECT room_id FROM guest_house_bookings
                WHERE status != 'Cancelled' AND (
                    (check_in_date <= ? AND check_out_date >= ?)
                    OR (check_in_date <= ? AND check_out_date >= ?)
                    OR (check_in_date >= ? AND check_out_date <= ?)
                )
            )
        """

        params = [check_out_date, check_in_date, check_in_date, check_out_date, check_in_date, check_out_date]

        # Add filters
        if capacity:
            base_query += " AND capacity >= ?"
            params.append(capacity)

        if beds:
            base_query += " AND beds >= ?"
            params.append(beds)

        if ac_required:
            base_query += " AND ac_available = ?"
            params.append(ac_required)

        if tv_required:
            base_query += " AND tv_available = ?"
            params.append(tv_required)

        if wifi_required:
            base_query += " AND wifi_available = ?"
            params.append(wifi_required)

        if max_price:
            base_query += " AND price_per_night <= ?"
            params.append(max_price)

        base_query += " ORDER BY price_per_night ASC"

        self.cur.execute(base_query, params)
        return self.cur.fetchall()

    def search_rooms_by_amenities(self, beds=None, ac=None, tv=None, wifi=None, room_type=None, max_price=None, min_price=None):
        """Search rooms by amenities and features"""
        query = "SELECT * FROM guest_house WHERE 1=1"
        params = []

        if beds:
            query += " AND beds = ?"
            params.append(beds)

        if ac:
            query += " AND ac_available = ?"
            params.append(ac)

        if tv:
            query += " AND tv_available = ?"
            params.append(tv)

        if wifi:
            query += " AND wifi_available = ?"
            params.append(wifi)

        if room_type:
            query += " AND room_type LIKE ?"
            params.append(f"%{room_type}%")

        if max_price:
            query += " AND price_per_night <= ?"
            params.append(max_price)

        if min_price:
            query += " AND price_per_night >= ?"
            params.append(min_price)

        query += " ORDER BY price_per_night ASC"

        self.cur.execute(query, params)
        return self.cur.fetchall()

    def book_guest_house(self, customer_id, room_id, check_out_date, id_card=None, photo=None, meal_plan=None, companions=None):
        """Book a guest house room with automatic check-in date and optional companions"""
        # Check if room is available
        self.cur.execute("SELECT status, capacity FROM guest_house WHERE id = ?", (room_id,))
        room_info = self.cur.fetchone()
        status = room_info[0]
        capacity = room_info[1]

        if status != "Available":
            raise ValueError("Room is not available for booking")

        # Check if companions exceed room capacity
        companion_count = len(companions) if companions else 0
        if companion_count + 1 > capacity:
            raise ValueError(f"Room capacity ({capacity}) exceeded with {companion_count + 1} guests")

        # Set check-in date to current date
        check_in_date = datetime.datetime.now().strftime("%Y-%m-%d")
        check_in_time = datetime.datetime.now().strftime("%H:%M:%S")

        # Calculate number of nights
        check_in = datetime.datetime.strptime(check_in_date, "%Y-%m-%d")
        check_out = datetime.datetime.strptime(check_out_date, "%Y-%m-%d")
        nights = (check_out - check_in).days

        if nights <= 0:
            raise ValueError("Check-out date must be after check-in date")

        # Get room price
        self.cur.execute("SELECT price_per_night FROM guest_house WHERE id = ?", (room_id,))
        price_per_night = self.cur.fetchone()[0]

        # Calculate total amount
        total_amount = nights * price_per_night

        # Add meal plan cost if provided (in Naira)
        meal_plan_cost = 0
        if meal_plan:
            if meal_plan == "Full Board":
                # Include meal costs for all guests (primary + companions)
                meal_plan_cost = 10000 * nights * (companion_count + 1)  # ₦10,000 per day per person for full board
            elif meal_plan == "Half Board":
                meal_plan_cost = 6000 * nights * (companion_count + 1)   # ₦6,000 per day per person for half board
            elif meal_plan == "Breakfast Only":
                meal_plan_cost = 3000 * nights * (companion_count + 1)   # ₦3,000 per day per person for breakfast only

            total_amount += meal_plan_cost

        # Create booking
        has_companions = 1 if companions else 0
        self.cur.execute("""
            INSERT INTO guest_house_bookings
            (customer_id, room_id, check_in_date, check_in_time, check_out_date,
             total_amount, id_card, photo, meal_plan, has_companions)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (customer_id, room_id, check_in_date, check_in_time, check_out_date,
              total_amount, id_card, photo, meal_plan, has_companions))
        self.conn.commit()
        booking_id = self.cur.lastrowid

        # Add companions if provided
        if companions:
            for companion in companions:
                self.add_guest_companion(
                    booking_id,
                    companion.get('name'),
                    companion.get('relationship'),
                    companion.get('id_card'),
                    companion.get('phone')
                )

        # Update room status
        self.cur.execute("""
            UPDATE guest_house SET status = 'Occupied' WHERE id = ?
        """, (room_id,))
        self.conn.commit()

        # Create invoice
        self.create_invoice(customer_id, total_amount, "Guest House", booking_id)

        # Record financial transaction
        self.add_financial_record("Guest House", "Income", total_amount,
                                 f"Room booking for {nights} nights with {companion_count + 1} guests", booking_id)

        return booking_id

    def get_guest_house_dashboard(self):
        """Get dashboard information for guest house"""
        # Get all rooms with their status
        self.cur.execute("""
            SELECT gh.id, gh.room_number, gh.room_type, gh.capacity,
                   gh.price_per_night, gh.status,
                   b.id as booking_id, b.check_in_date, b.check_in_time, b.check_out_date,
                   c.id as customer_id, c.name as customer_name, c.rank, c.department,
                   b.id_card, b.photo, b.meal_plan, b.total_amount
            FROM guest_house gh
            LEFT JOIN guest_house_bookings b ON gh.id = b.room_id AND gh.status = 'Occupied'
            LEFT JOIN customers c ON b.customer_id = c.id
            ORDER BY gh.room_number
        """)

        return self.cur.fetchall()

    def get_guest_house_financial_summary(self):
        """Get financial summary for guest house"""
        # Get total revenue for current month
        current_month = datetime.datetime.now().strftime("%Y-%m")

        self.cur.execute("""
            SELECT SUM(total_amount)
            FROM guest_house_bookings
            WHERE check_in_date LIKE ?
        """, (current_month + '%',))

        current_month_revenue = self.cur.fetchone()[0] or 0

        # Get total revenue for previous month
        previous_month = (datetime.datetime.now().replace(day=1) - datetime.timedelta(days=1)).strftime("%Y-%m")

        self.cur.execute("""
            SELECT SUM(total_amount)
            FROM guest_house_bookings
            WHERE check_in_date LIKE ?
        """, (previous_month + '%',))

        previous_month_revenue = self.cur.fetchone()[0] or 0

        # Get occupancy rate for current month
        self.cur.execute("""
            SELECT COUNT(DISTINCT room_id)
            FROM guest_house_bookings
            WHERE check_in_date LIKE ?
        """, (current_month + '%',))

        occupied_rooms = self.cur.fetchone()[0] or 0

        self.cur.execute("SELECT COUNT(*) FROM guest_house")
        total_rooms = self.cur.fetchone()[0]

        occupancy_rate = (occupied_rooms / total_rooms) * 100 if total_rooms > 0 else 0

        # Get upcoming checkouts (next 3 days)
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        three_days_later = (datetime.datetime.now() + datetime.timedelta(days=3)).strftime("%Y-%m-%d")

        self.cur.execute("""
            SELECT b.id, gh.room_number, c.name, b.check_out_date
            FROM guest_house_bookings b
            JOIN guest_house gh ON b.room_id = gh.id
            JOIN customers c ON b.customer_id = c.id
            WHERE b.check_out_date BETWEEN ? AND ?
            ORDER BY b.check_out_date
        """, (today, three_days_later))

        upcoming_checkouts = self.cur.fetchall()

        return {
            'current_month_revenue': current_month_revenue,
            'previous_month_revenue': previous_month_revenue,
            'occupancy_rate': occupancy_rate,
            'upcoming_checkouts': upcoming_checkouts
        }

    def add_guest_house_order(self, booking_id, order_type, items, total_amount):
        """Add an order (meal, drinks, etc.) to a guest house booking"""
        order_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        self.cur.execute("""
            INSERT INTO guest_house_orders
            (booking_id, order_type, order_date, items, total_amount)
            VALUES (?, ?, ?, ?, ?)
        """, (booking_id, order_type, order_date, items, total_amount))
        self.conn.commit()

        # Update the booking total amount
        self.cur.execute("""
            UPDATE guest_house_bookings
            SET total_amount = total_amount + ?
            WHERE id = ?
        """, (total_amount, booking_id))
        self.conn.commit()

        return self.cur.lastrowid

    def get_guest_house_orders(self, booking_id=None):
        """Get orders for a specific booking or all orders"""
        if booking_id:
            self.cur.execute("""
                SELECT o.*, gh.room_number, c.name as customer_name
                FROM guest_house_orders o
                JOIN guest_house_bookings b ON o.booking_id = b.id
                JOIN guest_house gh ON b.room_id = gh.id
                JOIN customers c ON b.customer_id = c.id
                WHERE o.booking_id = ?
                ORDER BY o.order_date DESC
            """, (booking_id,))
        else:
            self.cur.execute("""
                SELECT o.*, gh.room_number, c.name as customer_name
                FROM guest_house_orders o
                JOIN guest_house_bookings b ON o.booking_id = b.id
                JOIN guest_house gh ON b.room_id = gh.id
                JOIN customers c ON b.customer_id = c.id
                ORDER BY o.order_date DESC
            """)

        return self.cur.fetchall()

    # Invoice methods
    def create_invoice(self, customer_id, total_amount, module, reference_id):
        """Create a new invoice"""
        invoice_date = datetime.datetime.now().strftime("%Y-%m-%d")

        self.cur.execute("""
            INSERT INTO invoices
            (customer_id, invoice_date, total_amount, module, reference_id)
            VALUES (?, ?, ?, ?, ?)
        """, (customer_id, invoice_date, total_amount, module, reference_id))
        self.conn.commit()
        invoice_id = self.cur.lastrowid

        # Add invoice items based on the module
        if module == "Restaurant":
            self.add_restaurant_invoice_items(invoice_id, reference_id)
        elif module == "Inventory":
            self.add_inventory_invoice_items(invoice_id, reference_id)
        elif module == "Guest House":
            self.add_guest_house_invoice_items(invoice_id, reference_id)

        return invoice_id

    def add_restaurant_invoice_items(self, invoice_id, order_id):
        """Add restaurant order items to an invoice"""
        self.cur.execute("""
            SELECT m.item_name, oi.quantity, oi.price, (oi.quantity * oi.price) as total
            FROM order_items oi
            JOIN restaurant_menu m ON oi.menu_item_id = m.id
            WHERE oi.order_id = ?
        """, (order_id,))

        items = self.cur.fetchall()
        for item in items:
            item_name, quantity, unit_price, total_price = item
            self.cur.execute("""
                INSERT INTO invoice_items
                (invoice_id, item_description, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?)
            """, (invoice_id, item_name, quantity, unit_price, total_price))

        self.conn.commit()

    def add_inventory_invoice_items(self, invoice_id, transaction_id):
        """Add inventory items to an invoice"""
        # This would be implemented based on how inventory transactions are tracked
        pass

    def add_guest_house_invoice_items(self, invoice_id, booking_id):
        """Add guest house booking details to an invoice"""
        self.cur.execute("""
            SELECT gh.room_number, gh.room_type, b.check_in_date, b.check_out_date,
                   gh.price_per_night, b.total_amount
            FROM guest_house_bookings b
            JOIN guest_house gh ON b.room_id = gh.id
            WHERE b.id = ?
        """, (booking_id,))

        booking = self.cur.fetchone()
        if booking:
            room_number, room_type, check_in, check_out, price, total = booking
            check_in_obj = datetime.datetime.strptime(check_in, "%Y-%m-%d")
            check_out_obj = datetime.datetime.strptime(check_out, "%Y-%m-%d")
            nights = (check_out_obj - check_in_obj).days

            description = f"Room {room_number} ({room_type}): {check_in} to {check_out}"

            self.cur.execute("""
                INSERT INTO invoice_items
                (invoice_id, item_description, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?)
            """, (invoice_id, description, nights, price, total))

            self.conn.commit()

    def get_invoice(self, invoice_id):
        """Get invoice details with items"""
        self.cur.execute("""
            SELECT i.*, c.name as customer_name
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            WHERE i.id = ?
        """, (invoice_id,))

        invoice = self.cur.fetchone()

        if invoice:
            self.cur.execute("""
                SELECT * FROM invoice_items WHERE invoice_id = ?
            """, (invoice_id,))
            items = self.cur.fetchall()
            return (invoice, items)

        return None

    def initialize_guest_house_rooms(self):
        """Initialize the 6 guest house rooms if they don't exist"""
        # Check if rooms already exist
        self.cur.execute("SELECT COUNT(*) FROM guest_house")
        count = self.cur.fetchone()[0]

        if count == 0:
            # Create 6 enhanced rooms with detailed amenities and rates in Naira
            rooms = [
                ("101", "Standard Single", 1, 1, "Yes", "Yes", "Yes", 15000.00, "Available", "Air conditioning, TV, WiFi, Private bathroom", None, 1, "Medium", "Private", "No"),
                ("102", "Standard Double", 2, 2, "Yes", "Yes", "Yes", 25000.00, "Available", "Air conditioning, TV, WiFi, Private bathroom, Double bed", None, 1, "Large", "Private", "No"),
                ("103", "Deluxe Single", 1, 1, "Yes", "Yes", "Yes", 30000.00, "Available", "Air conditioning, TV, WiFi, Private bathroom, Mini fridge", None, 2, "Large", "Private", "Yes"),
                ("104", "Deluxe Double", 2, 2, "Yes", "Yes", "Yes", 40000.00, "Available", "Air conditioning, TV, WiFi, Private bathroom, Mini fridge, Sofa", None, 2, "Extra Large", "Private", "Yes"),
                ("105", "Executive Suite", 2, 2, "Yes", "Yes", "Yes", 60000.00, "Available", "Air conditioning, TV, WiFi, Private bathroom, Mini fridge, Sofa, Work desk", None, 3, "Suite", "Private", "Yes"),
                ("106", "Presidential Suite", 4, 3, "Yes", "Yes", "Yes", 100000.00, "Available", "Air conditioning, TV, WiFi, Private bathroom, Mini fridge, Sofa, Work desk, Dining area", None, 3, "Presidential", "Private", "Yes")
            ]

            for room in rooms:
                self.cur.execute("""
                    INSERT INTO guest_house
                    (room_number, room_type, capacity, beds, ac_available, tv_available, wifi_available,
                     price_per_night, status, features, image, floor_number, room_size, bathroom_type, balcony)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, room)

            self.conn.commit()
            return True
        return False

    # WiFi QR Code Access System
    def generate_wifi_access_code(self, customer_id, booking_id=None, order_id=None, access_type="Guest", duration_hours=24):
        """Generate WiFi access code and QR code for customer"""
        import secrets
        import string

        # Generate unique access code
        access_code = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))

        # Generate WiFi password
        wifi_password = f"HUB{access_code[:4]}{datetime.datetime.now().strftime('%m%d')}"

        # Calculate expiry date
        expiry_date = (datetime.datetime.now() + datetime.timedelta(hours=duration_hours)).strftime("%Y-%m-%d %H:%M:%S")

        # Create WiFi QR code data (standard WiFi QR format)
        wifi_ssid = "HUB_GUEST_WIFI"
        qr_data = f"WIFI:T:WPA;S:{wifi_ssid};P:{wifi_password};H:false;;"

        # Generate QR code image
        qr_code_image = self.generate_qr_code(qr_data)

        # Store in database
        self.cur.execute("""
            INSERT INTO wifi_access_codes
            (customer_id, booking_id, order_id, access_code, qr_code_data, wifi_password, expiry_date, access_type)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (customer_id, booking_id, order_id, access_code, qr_code_image, wifi_password, expiry_date, access_type))

        wifi_id = self.cur.lastrowid

        # Update customer rewards
        self.update_customer_rewards(customer_id, 'wifi_access')

        self.conn.commit()

        return {
            'wifi_id': wifi_id,
            'access_code': access_code,
            'wifi_password': wifi_password,
            'qr_code': qr_code_image,
            'expiry_date': expiry_date,
            'ssid': wifi_ssid
        }

    def update_customer_rewards(self, customer_id, action_type):
        """Update customer rewards based on actions"""
        # Check if customer has rewards record
        self.cur.execute("SELECT id, points_earned, wifi_access_count, app_usage_count FROM customer_rewards WHERE customer_id = ?", (customer_id,))
        reward_record = self.cur.fetchone()

        points_to_add = 0
        if action_type == 'wifi_access':
            points_to_add = 5
        elif action_type == 'app_download':
            points_to_add = 20
        elif action_type == 'booking':
            points_to_add = 50
        elif action_type == 'restaurant_order':
            points_to_add = 10

        if reward_record:
            # Update existing record
            new_points = reward_record[1] + points_to_add
            new_wifi_count = reward_record[2] + (1 if action_type == 'wifi_access' else 0)
            new_app_count = reward_record[3] + (1 if action_type == 'app_download' else 0)

            # Determine tier level
            tier_level = self.calculate_tier_level(new_points)

            self.cur.execute("""
                UPDATE customer_rewards
                SET points_earned = ?, current_balance = current_balance + ?,
                    wifi_access_count = ?, app_usage_count = ?, tier_level = ?,
                    last_activity_date = CURRENT_TIMESTAMP
                WHERE customer_id = ?
            """, (new_points, points_to_add, new_wifi_count, new_app_count, tier_level, customer_id))
        else:
            # Create new record
            tier_level = self.calculate_tier_level(points_to_add)
            wifi_count = 1 if action_type == 'wifi_access' else 0
            app_count = 1 if action_type == 'app_download' else 0

            self.cur.execute("""
                INSERT INTO customer_rewards
                (customer_id, points_earned, current_balance, tier_level, wifi_access_count, app_usage_count)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (customer_id, points_to_add, points_to_add, tier_level, wifi_count, app_count))

        self.conn.commit()

    def calculate_tier_level(self, points):
        """Calculate customer tier level based on points"""
        if points >= 1000:
            return 'Platinum'
        elif points >= 500:
            return 'Gold'
        elif points >= 200:
            return 'Silver'
        else:
            return 'Bronze'

    def get_customer_wifi_access(self, customer_id):
        """Get customer's WiFi access codes"""
        self.cur.execute("""
            SELECT w.*, c.name as customer_name
            FROM wifi_access_codes w
            JOIN customers c ON w.customer_id = c.id
            WHERE w.customer_id = ? AND w.status = 'Active'
            ORDER BY w.created_date DESC
        """, (customer_id,))
        return self.cur.fetchall()

    def validate_wifi_access(self, access_code):
        """Validate WiFi access code"""
        self.cur.execute("""
            SELECT * FROM wifi_access_codes
            WHERE access_code = ? AND status = 'Active'
            AND datetime(expiry_date) > datetime('now')
        """, (access_code,))
        return self.cur.fetchone()

    def track_app_download(self, customer_id, device_type, platform, referral_source='QR_Code'):
        """Track mobile app download"""
        self.cur.execute("""
            INSERT INTO app_downloads
            (customer_id, device_type, platform, referral_source)
            VALUES (?, ?, ?, ?)
        """, (customer_id, device_type, platform, referral_source))

        # Update customer rewards
        self.update_customer_rewards(customer_id, 'app_download')

        self.conn.commit()
        return self.cur.lastrowid

    # Enhanced Payment Methods
    def process_payment(self, booking_id, customer_id, amount, payment_method, transaction_reference=None, payment_gateway=None, created_by=None):
        """Process a payment transaction"""
        self.cur.execute("""
            INSERT INTO payment_transactions
            (booking_id, customer_id, amount, payment_method, transaction_reference, payment_gateway, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (booking_id, customer_id, amount, payment_method, transaction_reference, payment_gateway, created_by))

        transaction_id = self.cur.lastrowid

        # Update booking payment status
        self.cur.execute("""
            UPDATE guest_house_bookings
            SET payment_status = 'Completed', payment_method = ?
            WHERE id = ?
        """, (payment_method, booking_id))

        self.conn.commit()
        return transaction_id

    def get_payment_history(self, customer_id=None, booking_id=None):
        """Get payment history"""
        if booking_id:
            self.cur.execute("""
                SELECT pt.*, c.name as customer_name, gh.room_number
                FROM payment_transactions pt
                JOIN customers c ON pt.customer_id = c.id
                JOIN guest_house_bookings ghb ON pt.booking_id = ghb.id
                JOIN guest_house gh ON ghb.room_id = gh.id
                WHERE pt.booking_id = ?
                ORDER BY pt.transaction_date DESC
            """, (booking_id,))
        elif customer_id:
            self.cur.execute("""
                SELECT pt.*, c.name as customer_name, gh.room_number
                FROM payment_transactions pt
                JOIN customers c ON pt.customer_id = c.id
                LEFT JOIN guest_house_bookings ghb ON pt.booking_id = ghb.id
                LEFT JOIN guest_house gh ON ghb.room_id = gh.id
                WHERE pt.customer_id = ?
                ORDER BY pt.transaction_date DESC
            """, (customer_id,))
        else:
            self.cur.execute("""
                SELECT pt.*, c.name as customer_name, gh.room_number
                FROM payment_transactions pt
                JOIN customers c ON pt.customer_id = c.id
                LEFT JOIN guest_house_bookings ghb ON pt.booking_id = ghb.id
                LEFT JOIN guest_house gh ON ghb.room_id = gh.id
                ORDER BY pt.transaction_date DESC
            """)

        return self.cur.fetchall()

    def get_room_details_with_amenities(self, room_id):
        """Get detailed room information including all amenities"""
        self.cur.execute("""
            SELECT gh.*, u.full_name as assigned_staff_name
            FROM guest_house gh
            LEFT JOIN users u ON gh.assigned_staff_id = u.id
            WHERE gh.id = ?
        """, (room_id,))
        return self.cur.fetchone()

    def update_room_amenities(self, room_id, beds=None, ac_available=None, tv_available=None, wifi_available=None, features=None):
        """Update room amenities"""
        updates = []
        params = []

        if beds is not None:
            updates.append("beds = ?")
            params.append(beds)
        if ac_available is not None:
            updates.append("ac_available = ?")
            params.append(ac_available)
        if tv_available is not None:
            updates.append("tv_available = ?")
            params.append(tv_available)
        if wifi_available is not None:
            updates.append("wifi_available = ?")
            params.append(wifi_available)
        if features is not None:
            updates.append("features = ?")
            params.append(features)

        if updates:
            query = f"UPDATE guest_house SET {', '.join(updates)} WHERE id = ?"
            params.append(room_id)
            self.cur.execute(query, params)
            self.conn.commit()
            return True
        return False

    def assign_staff_to_room(self, room_id, staff_id, assignment_type="Cleaning", shift_start=None, shift_end=None):
        """Assign staff to a room"""
        # Update room assignment
        self.cur.execute("UPDATE guest_house SET assigned_staff_id = ? WHERE id = ?", (staff_id, room_id))

        # Add to staff assignments log
        self.cur.execute("""
            INSERT INTO staff_assignments (staff_id, room_id, assignment_type, shift_start, shift_end)
            VALUES (?, ?, ?, ?, ?)
        """, (staff_id, room_id, assignment_type, shift_start, shift_end))

        self.conn.commit()
        return self.cur.lastrowid

    def schedule_room_maintenance(self, room_id, maintenance_type, description, scheduled_date, assigned_staff=None, priority="Medium"):
        """Schedule room maintenance"""
        self.cur.execute("""
            INSERT INTO room_maintenance_log
            (room_id, maintenance_type, description, scheduled_date, assigned_staff, priority)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (room_id, maintenance_type, description, scheduled_date, assigned_staff, priority))

        self.conn.commit()
        return self.cur.lastrowid

    # User and Access Control Methods
    def add_user(self, username, password, full_name, role, email=None, phone=None):
        """Add a new user with role-based access"""
        # Check if username already exists
        self.cur.execute("SELECT COUNT(*) FROM users WHERE username = ?", (username,))
        if self.cur.fetchone()[0] > 0:
            raise ValueError(f"Username '{username}' already exists")

        # Add user
        self.cur.execute("""
            INSERT INTO users (username, password, full_name, role, email, phone)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (username, password, full_name, role, email, phone))
        self.conn.commit()
        user_id = self.cur.lastrowid

        # Set default permissions based on role
        if role == "CSO":
            # CSO has full access to all modules
            modules = ["Inventory", "Restaurant", "Bar", "Gym", "Guest House", "Customers", "Invoices", "Maintenance", "Reports"]
            for module in modules:
                self.cur.execute("""
                    INSERT INTO user_permissions (user_id, module, can_view, can_add, can_edit, can_delete)
                    VALUES (?, ?, 1, 1, 1, 1)
                """, (user_id, module))
        elif role == "Wine Officer":
            # Wine Officer has full access to Bar and Restaurant modules
            self.cur.execute("""
                INSERT INTO user_permissions (user_id, module, can_view, can_add, can_edit, can_delete)
                VALUES (?, ?, 1, 1, 1, 1)
            """, (user_id, "Bar"))
            self.cur.execute("""
                INSERT INTO user_permissions (user_id, module, can_view, can_add, can_edit, can_delete)
                VALUES (?, ?, 1, 1, 1, 1)
            """, (user_id, "Restaurant"))
            # View-only access to other modules
            modules = ["Inventory", "Gym", "Guest House", "Customers", "Invoices", "Reports"]
            for module in modules:
                self.cur.execute("""
                    INSERT INTO user_permissions (user_id, module, can_view, can_add, can_edit, can_delete)
                    VALUES (?, ?, 1, 0, 0, 0)
                """, (user_id, module))
        elif role == "Property Officer":
            # Property Officer has full access to Maintenance module
            self.cur.execute("""
                INSERT INTO user_permissions (user_id, module, can_view, can_add, can_edit, can_delete)
                VALUES (?, ?, 1, 1, 1, 1)
            """, (user_id, "Maintenance"))
            # View-only access to other modules
            modules = ["Inventory", "Bar", "Restaurant", "Gym", "Guest House", "Customers", "Reports"]
            for module in modules:
                self.cur.execute("""
                    INSERT INTO user_permissions (user_id, module, can_view, can_add, can_edit, can_delete)
                    VALUES (?, ?, 1, 0, 0, 0)
                """, (user_id, module))

        self.conn.commit()
        return user_id

    def authenticate_user(self, username, password):
        """Authenticate a user and update last login time"""
        self.cur.execute("""
            SELECT id, role, status FROM users
            WHERE username = ? AND password = ?
        """, (username, password))

        user = self.cur.fetchone()
        if user and user[2] == 'Active':
            # Update last login time
            last_login = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.cur.execute("UPDATE users SET last_login = ? WHERE id = ?", (last_login, user[0]))
            self.conn.commit()
            return user[0], user[1]  # Return user_id and role

        return None, None

    def get_user_permissions(self, user_id):
        """Get permissions for a user"""
        self.cur.execute("""
            SELECT module, can_view, can_add, can_edit, can_delete
            FROM user_permissions
            WHERE user_id = ?
        """, (user_id,))

        permissions = {}
        for row in self.cur.fetchall():
            module, can_view, can_add, can_edit, can_delete = row
            permissions[module] = {
                'view': bool(can_view),
                'add': bool(can_add),
                'edit': bool(can_edit),
                'delete': bool(can_delete)
            }

        return permissions

    # Guest Companions Methods
    def add_guest_companion(self, booking_id, name, relationship=None, id_card=None, phone=None):
        """Add a companion to a guest house booking"""
        self.cur.execute("""
            INSERT INTO guest_companions (booking_id, name, relationship, id_card, phone)
            VALUES (?, ?, ?, ?, ?)
        """, (booking_id, name, relationship, id_card, phone))

        # Update the booking to indicate it has companions
        self.cur.execute("UPDATE guest_house_bookings SET has_companions = 1 WHERE id = ?", (booking_id,))

        self.conn.commit()
        return self.cur.lastrowid

    def get_booking_companions(self, booking_id):
        """Get all companions for a booking"""
        self.cur.execute("SELECT * FROM guest_companions WHERE booking_id = ?", (booking_id,))
        return self.cur.fetchall()

    # Bar and Food Inventory Methods
    def add_bar_item(self, item_name, category, supplier, price, cost_price, quantity=1, expiration_date=None, notes=None):
        """Add a new item to bar inventory"""
        # Generate QR code
        current_time = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        qr_data = f"BAR:{item_name}|ID:{current_time}|PRICE:{price}"
        qr_code = self.generate_qr_code(qr_data)

        purchase_date = datetime.datetime.now().strftime("%Y-%m-%d")

        self.cur.execute("""
            INSERT INTO bar_inventory
            (item_name, category, supplier, price, cost_price, quantity, expiration_date, purchase_date, qr_code, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (item_name, category, supplier, price, cost_price, quantity, expiration_date, purchase_date, qr_code, notes))

        self.conn.commit()

        # Record financial transaction
        item_id = self.cur.lastrowid
        total_cost = cost_price * quantity
        self.add_financial_record("Bar", "Expense", total_cost, f"Purchase of {quantity} {item_name}", item_id)

        return item_id

    def add_food_item(self, item_name, category, supplier, price, cost_price, quantity=1, expiration_date=None, notes=None):
        """Add a new item to food inventory"""
        # Generate QR code
        current_time = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        qr_data = f"FOOD:{item_name}|ID:{current_time}|PRICE:{price}"
        qr_code = self.generate_qr_code(qr_data)

        purchase_date = datetime.datetime.now().strftime("%Y-%m-%d")

        self.cur.execute("""
            INSERT INTO food_inventory
            (item_name, category, supplier, price, cost_price, quantity, expiration_date, purchase_date, qr_code, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (item_name, category, supplier, price, cost_price, quantity, expiration_date, purchase_date, qr_code, notes))

        self.conn.commit()

        # Record financial transaction
        item_id = self.cur.lastrowid
        total_cost = cost_price * quantity
        self.add_financial_record("Food", "Expense", total_cost, f"Purchase of {quantity} {item_name}", item_id)

        return item_id

    # Financial Methods
    def add_financial_record(self, module, record_type, amount, description=None, reference_id=None):
        """Add a financial record"""
        record_date = datetime.datetime.now().strftime("%Y-%m-%d")

        self.cur.execute("""
            INSERT INTO financial_records
            (module, record_date, record_type, amount, description, reference_id)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (module, record_date, record_type, amount, description, reference_id))

        self.conn.commit()
        return self.cur.lastrowid

    def generate_profit_loss_report(self, module, period, start_date=None, end_date=None):
        """Generate a profit/loss report for a module and period"""
        if not start_date:
            # Default to current month if no dates provided
            today = datetime.datetime.now()
            start_date = datetime.datetime(today.year, today.month, 1).strftime("%Y-%m-%d")
            end_date = today.strftime("%Y-%m-%d")

        # Get revenue (income)
        self.cur.execute("""
            SELECT SUM(amount) FROM financial_records
            WHERE module = ? AND record_type = 'Income' AND record_date BETWEEN ? AND ?
        """, (module, start_date, end_date))

        revenue = self.cur.fetchone()[0] or 0

        # Get expenses
        self.cur.execute("""
            SELECT SUM(amount) FROM financial_records
            WHERE module = ? AND record_type = 'Expense' AND record_date BETWEEN ? AND ?
        """, (module, start_date, end_date))

        expenses = self.cur.fetchone()[0] or 0

        # Calculate profit
        profit = revenue - expenses

        # Save the report
        self.cur.execute("""
            INSERT INTO profit_loss_records
            (module, period, start_date, end_date, revenue, expenses, profit)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (module, period, start_date, end_date, revenue, expenses, profit))

        self.conn.commit()

        return {
            'module': module,
            'period': period,
            'start_date': start_date,
            'end_date': end_date,
            'revenue': revenue,
            'expenses': expenses,
            'profit': profit
        }

    # Maintenance Request Methods
    def add_maintenance_request(self, requester_id, location, issue_type, description, priority="Medium"):
        """Add a new maintenance request"""
        request_date = datetime.datetime.now().strftime("%Y-%m-%d")
        request_time = datetime.datetime.now().strftime("%H:%M:%S")

        self.cur.execute("""
            INSERT INTO maintenance_requests
            (requester_id, request_date, request_time, location, issue_type, description, priority)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (requester_id, request_date, request_time, location, issue_type, description, priority))

        self.conn.commit()
        return self.cur.lastrowid

    def assign_maintenance_request(self, request_id, assigned_to):
        """Assign a maintenance request to a user"""
        self.cur.execute("""
            UPDATE maintenance_requests
            SET assigned_to = ?, status = 'Assigned'
            WHERE id = ?
        """, (assigned_to, request_id))

        self.conn.commit()

    def complete_maintenance_request(self, request_id, notes=None):
        """Mark a maintenance request as completed"""
        completion_date = datetime.datetime.now().strftime("%Y-%m-%d")

        self.cur.execute("""
            UPDATE maintenance_requests
            SET status = 'Completed', completion_date = ?, notes = ?
            WHERE id = ?
        """, (completion_date, notes, request_id))

        self.conn.commit()

    def get_maintenance_requests(self, status=None, assigned_to=None):
        """Get maintenance requests, optionally filtered by status or assignee"""
        query = "SELECT * FROM maintenance_requests"
        params = []

        if status and assigned_to:
            query += " WHERE status = ? AND assigned_to = ?"
            params.extend([status, assigned_to])
        elif status:
            query += " WHERE status = ?"
            params.append(status)
        elif assigned_to:
            query += " WHERE assigned_to = ?"
            params.append(assigned_to)

        query += " ORDER BY priority, request_date, request_time"

        self.cur.execute(query, params)
        return self.cur.fetchall()

    # Mess Member Methods
    def generate_membership_number(self, membership_type):
        """Generate a unique membership number based on type and current count"""
        # Get current year
        year = datetime.datetime.now().strftime("%Y")

        # Get prefix based on membership type
        prefix = "HM" if membership_type == "Honorary" else "MM"

        # Get current count of members of this type
        self.cur.execute("""
            SELECT COUNT(*) FROM mess_members WHERE membership_type = ?
        """, (membership_type,))

        count = self.cur.fetchone()[0] + 1

        # Format: [PREFIX]-[YEAR]-[SEQUENTIAL NUMBER]
        membership_number = f"{prefix}-{year}-{count:04d}"

        return membership_number

    def register_mess_member(self, customer_id, membership_type, occupation=None, company=None,
                           position=None, passport_photo=None, credit_limit=0, duration_months=12):
        """Register a new mess member with enhanced details for honorary members"""
        # Generate membership number
        membership_number = self.generate_membership_number(membership_type)

        join_date = datetime.datetime.now().strftime("%Y-%m-%d")
        expiry_date = (datetime.datetime.now() + datetime.timedelta(days=30*duration_months)).strftime("%Y-%m-%d")

        # Set verification status based on membership type
        verification_status = "Pending" if membership_type == "Honorary" else "Approved"

        self.cur.execute("""
            INSERT INTO mess_members
            (customer_id, membership_number, membership_type, join_date, expiry_date,
             credit_limit, occupation, company, position, passport_photo, verification_status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (customer_id, membership_number, membership_type, join_date, expiry_date,
             credit_limit, occupation, company, position, passport_photo, verification_status))

        self.conn.commit()
        return self.cur.lastrowid

    def add_mess_member_sponsor(self, member_id, sponsor_id, relationship, comments=None):
        """Add a sponsor for a mess member"""
        self.cur.execute("""
            INSERT INTO mess_member_sponsors
            (member_id, sponsor_id, relationship, comments)
            VALUES (?, ?, ?, ?)
        """, (member_id, sponsor_id, relationship, comments))

        self.conn.commit()
        return self.cur.lastrowid

    def verify_mess_member(self, member_id, verified_by, status="Approved"):
        """Verify a mess member application"""
        verification_date = datetime.datetime.now().strftime("%Y-%m-%d")

        self.cur.execute("""
            UPDATE mess_members
            SET verification_status = ?, verification_date = ?, verified_by = ?
            WHERE id = ?
        """, (status, verification_date, verified_by, member_id))

        self.conn.commit()

    def get_mess_member_sponsors(self, member_id):
        """Get all sponsors for a mess member"""
        self.cur.execute("""
            SELECT s.*, c.name, c.rank, c.department, c.phone, c.email
            FROM mess_member_sponsors s
            JOIN customers c ON s.sponsor_id = c.id
            WHERE s.member_id = ?
        """, (member_id,))

        return self.cur.fetchall()

    def update_mess_member_balance(self, member_id, amount):
        """Update a mess member's balance (positive for credit, negative for debit)"""
        self.cur.execute("SELECT current_balance FROM mess_members WHERE id = ?", (member_id,))
        current_balance = self.cur.fetchone()[0]
        new_balance = current_balance + amount

        self.cur.execute("UPDATE mess_members SET current_balance = ? WHERE id = ?", (new_balance, member_id))
        self.conn.commit()

        return new_balance

    def get_mess_member(self, member_id=None, customer_id=None, membership_number=None):
        """Get mess member details by ID, customer ID, or membership number"""
        if member_id:
            self.cur.execute("""
                SELECT m.*, c.name, c.rank, c.department, c.phone, c.email, c.address
                FROM mess_members m
                JOIN customers c ON m.customer_id = c.id
                WHERE m.id = ?
            """, (member_id,))
        elif customer_id:
            self.cur.execute("""
                SELECT m.*, c.name, c.rank, c.department, c.phone, c.email, c.address
                FROM mess_members m
                JOIN customers c ON m.customer_id = c.id
                WHERE m.customer_id = ?
            """, (customer_id,))
        elif membership_number:
            self.cur.execute("""
                SELECT m.*, c.name, c.rank, c.department, c.phone, c.email, c.address
                FROM mess_members m
                JOIN customers c ON m.customer_id = c.id
                WHERE m.membership_number = ?
            """, (membership_number,))
        else:
            return None

        return self.cur.fetchone()

    def get_all_mess_members(self, membership_type=None, status=None, verification_status=None):
        """Get all mess members, optionally filtered by type, status, or verification status"""
        query = """
            SELECT m.*, c.name, c.rank, c.department, c.phone, c.email, c.address
            FROM mess_members m
            JOIN customers c ON m.customer_id = c.id
        """

        params = []
        where_clauses = []

        if membership_type:
            where_clauses.append("m.membership_type = ?")
            params.append(membership_type)

        if status:
            where_clauses.append("m.status = ?")
            params.append(status)

        if verification_status:
            where_clauses.append("m.verification_status = ?")
            params.append(verification_status)

        if where_clauses:
            query += " WHERE " + " AND ".join(where_clauses)

        query += " ORDER BY m.join_date DESC"

        self.cur.execute(query, params)
        return self.cur.fetchall()

    # News Feed Methods
    def add_news_item(self, category, title, content, expiry_days=7, image=None):
        """Add a news item to the feed"""
        publish_date = datetime.datetime.now().strftime("%Y-%m-%d")
        publish_time = datetime.datetime.now().strftime("%H:%M:%S")
        expiry_date = (datetime.datetime.now() + datetime.timedelta(days=expiry_days)).strftime("%Y-%m-%d")

        self.cur.execute("""
            INSERT INTO news_feed
            (category, title, content, publish_date, publish_time, expiry_date, image)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (category, title, content, publish_date, publish_time, expiry_date, image))

        self.conn.commit()
        return self.cur.lastrowid

    def get_news_feed(self, category=None, limit=10):
        """Get news feed items, optionally filtered by category"""
        today = datetime.datetime.now().strftime("%Y-%m-%d")

        if category:
            self.cur.execute("""
                SELECT * FROM news_feed
                WHERE category = ? AND expiry_date >= ? AND status = 'Active'
                ORDER BY publish_date DESC, publish_time DESC
                LIMIT ?
            """, (category, today, limit))
        else:
            self.cur.execute("""
                SELECT * FROM news_feed
                WHERE expiry_date >= ? AND status = 'Active'
                ORDER BY publish_date DESC, publish_time DESC
                LIMIT ?
            """, (today, limit))

        return self.cur.fetchall()

    # Football Updates Methods
    def add_football_match(self, team_home, team_away, match_date, league=None):
        """Add a football match to the updates"""
        self.cur.execute("""
            INSERT INTO football_updates
            (team_home, team_away, match_date, league, status)
            VALUES (?, ?, ?, ?, 'Upcoming')
        """, (team_home, team_away, match_date, league))

        self.conn.commit()
        return self.cur.lastrowid

    def update_match_score(self, match_id, score_home, score_away, status="Completed", highlights=None):
        """Update a football match score"""
        self.cur.execute("""
            UPDATE football_updates
            SET score_home = ?, score_away = ?, status = ?, highlights = ?
            WHERE id = ?
        """, (score_home, score_away, status, highlights, match_id))

        self.conn.commit()

    def get_football_updates(self, status=None, limit=10):
        """Get football updates, optionally filtered by status"""
        if status:
            self.cur.execute("""
                SELECT * FROM football_updates
                WHERE status = ?
                ORDER BY match_date DESC
                LIMIT ?
            """, (status, limit))
        else:
            self.cur.execute("""
                SELECT * FROM football_updates
                ORDER BY
                    CASE
                        WHEN status = 'Live' THEN 1
                        WHEN status = 'Upcoming' THEN 2
                        ELSE 3
                    END,
                    match_date DESC
                LIMIT ?
            """, (limit,))

        return self.cur.fetchall()

    # Dashboard Methods
    def get_system_dashboard(self):
        """Get overview data for the main dashboard"""
        # Get current date
        today = datetime.datetime.now().strftime("%Y-%m-%d")

        # Get guest house occupancy
        self.cur.execute("SELECT COUNT(*) FROM guest_house WHERE status = 'Occupied'")
        occupied_rooms = self.cur.fetchone()[0] or 0

        self.cur.execute("SELECT COUNT(*) FROM guest_house")
        total_rooms = self.cur.fetchone()[0] or 0

        occupancy_rate = (occupied_rooms / total_rooms) * 100 if total_rooms > 0 else 0

        # Get today's restaurant orders
        self.cur.execute("""
            SELECT COUNT(*), SUM(total_amount) FROM restaurant_orders
            WHERE order_date LIKE ?
        """, (today + '%',))

        restaurant_orders = self.cur.fetchone()
        restaurant_order_count = restaurant_orders[0] or 0
        restaurant_revenue = restaurant_orders[1] or 0

        # Get bar inventory value
        self.cur.execute("""
            SELECT SUM(quantity * price) FROM bar_inventory
        """)
        bar_inventory_value = self.cur.fetchone()[0] or 0

        # Get food inventory value
        self.cur.execute("""
            SELECT SUM(quantity * price) FROM food_inventory
        """)
        food_inventory_value = self.cur.fetchone()[0] or 0

        # Get pending maintenance requests
        self.cur.execute("""
            SELECT COUNT(*) FROM maintenance_requests
            WHERE status = 'Pending'
        """)
        pending_maintenance = self.cur.fetchone()[0] or 0

        # Get active mess members
        self.cur.execute("""
            SELECT COUNT(*) FROM mess_members
            WHERE status = 'Active'
        """)
        active_members = self.cur.fetchone()[0] or 0

        # Get latest news
        self.cur.execute("""
            SELECT * FROM news_feed
            WHERE expiry_date >= ? AND status = 'Active'
            ORDER BY publish_date DESC, publish_time DESC
            LIMIT 5
        """, (today,))
        latest_news = self.cur.fetchall()

        # Get upcoming football matches
        self.cur.execute("""
            SELECT * FROM football_updates
            WHERE status = 'Upcoming' OR status = 'Live'
            ORDER BY match_date
            LIMIT 5
        """)
        upcoming_matches = self.cur.fetchall()

        return {
            'guest_house': {
                'occupied_rooms': occupied_rooms,
                'total_rooms': total_rooms,
                'occupancy_rate': occupancy_rate
            },
            'restaurant': {
                'today_orders': restaurant_order_count,
                'today_revenue': restaurant_revenue
            },
            'inventory': {
                'bar_value': bar_inventory_value,
                'food_value': food_inventory_value
            },
            'maintenance': {
                'pending_requests': pending_maintenance
            },
            'mess': {
                'active_members': active_members
            },
            'news': latest_news,
            'football': upcoming_matches
        }

    def __del__(self):
        """Close the database connection when the object is destroyed"""
        self.conn.close()
