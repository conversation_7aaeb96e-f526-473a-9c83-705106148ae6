import tkinter as tk
from tkinter import messagebox, Menu, filedialog
import os
import csv
from db import Database


db = Database('store.db')



class Application(tk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.master = master
        master.title('InventoryPro: Parts & Supply Chain Manager')

        master.geometry("800x600")

        # Create menu bar
        self.create_menu()

        # Create status bar
        self.status_text = tk.StringVar()
        self.status_text.set('Welcome to InventoryPro')
        self.status_bar = tk.Label(master, textvariable=self.status_text, bd=1, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        self.create_widgets()

        self.selected_item = 0

        self.populate_list()

    def create_menu(self):
        # Create main menu
        self.menu_bar = Menu(self.master)
        self.master.config(menu=self.menu_bar)

        # File menu
        file_menu = Menu(self.menu_bar, tearoff=0)
        file_menu.add_command(label="Export to CSV", command=self.export_to_csv)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.master.destroy)
        self.menu_bar.add_cascade(label="File", menu=file_menu)

        # Edit menu
        edit_menu = Menu(self.menu_bar, tearoff=0)
        edit_menu.add_command(label="Clear All Fields", command=self.clear_text)
        self.menu_bar.add_cascade(label="Edit", menu=edit_menu)

        # Help menu
        help_menu = Menu(self.menu_bar, tearoff=0)
        help_menu.add_command(label="About", command=self.show_about)
        self.menu_bar.add_cascade(label="Help", menu=help_menu)

    def export_to_csv(self):
        filename = filedialog.asksaveasfilename(
            initialdir="/",
            title="Export to CSV",
            filetypes=(("CSV files", "*.csv"), ("All files", "*.*"))
        )

        if filename:
            if not filename.endswith('.csv'):
                filename += '.csv'

            with open(filename, 'w', newline='') as csvfile:
                csv_writer = csv.writer(csvfile)
                csv_writer.writerow(['ID', 'Part Name', 'Customer', 'Retailer', 'Price'])

                for row in db.fetch():
                    csv_writer.writerow(row)

            self.status_text.set(f'Data exported to {os.path.basename(filename)}')

    def show_about(self):
        messagebox.showinfo("About InventoryPro", "InventoryPro: Parts & Supply Chain Manager\nVersion 1.0\n\nA comprehensive inventory management system for tracking parts, customers, and retailers.")

    def create_widgets(self):

        self.part_text = tk.StringVar()
        self.part_label = tk.Label(
            self.master, text='Part Name', font=('bold', 14), pady=20)
        self.part_label.grid(row=0, column=0, sticky=tk.W)
        self.part_entry = tk.Entry(self.master, textvariable=self.part_text)
        self.part_entry.grid(row=0, column=1)

        self.customer_text = tk.StringVar()
        self.customer_label = tk.Label(
            self.master, text='Customer', font=('bold', 14))
        self.customer_label.grid(row=0, column=2, sticky=tk.W)
        self.customer_entry = tk.Entry(
            self.master, textvariable=self.customer_text)
        self.customer_entry.grid(row=0, column=3)

        self.retailer_text = tk.StringVar()
        self.retailer_label = tk.Label(
            self.master, text='Retailer', font=('bold', 14))
        self.retailer_label.grid(row=1, column=0, sticky=tk.W)
        self.retailer_entry = tk.Entry(
            self.master, textvariable=self.retailer_text)
        self.retailer_entry.grid(row=1, column=1)

        self.price_text = tk.StringVar()
        self.price_label = tk.Label(
            self.master, text='Price', font=('bold', 14))
        self.price_label.grid(row=1, column=2, sticky=tk.W)
        self.price_entry = tk.Entry(self.master, textvariable=self.price_text)
        self.price_entry.grid(row=1, column=3)


        self.parts_list = tk.Listbox(self.master, height=8, width=50, border=0)
        self.parts_list.grid(row=3, column=0, columnspan=3,
                             rowspan=6, pady=20, padx=20)

        self.scrollbar = tk.Scrollbar(self.master)
        self.scrollbar.grid(row=3, column=3)

        self.parts_list.configure(yscrollcommand=self.scrollbar.set)
        self.scrollbar.configure(command=self.parts_list.yview)


        self.parts_list.bind('<<ListboxSelect>>', self.select_item)


        self.add_btn = tk.Button(
            self.master, text="Add Part", width=12, command=self.add_item)
        self.add_btn.grid(row=2, column=0, pady=20)

        self.remove_btn = tk.Button(
            self.master, text="Remove Part", width=12, command=self.remove_item)
        self.remove_btn.grid(row=2, column=1)

        self.update_btn = tk.Button(
            self.master, text="Update Part", width=12, command=self.update_item)
        self.update_btn.grid(row=2, column=2)

        self.exit_btn = tk.Button(
            self.master, text="Clear Input", width=12, command=self.clear_text)
        self.exit_btn.grid(row=2, column=3)

    def populate_list(self):

        self.parts_list.delete(0, tk.END)

        for row in db.fetch():

            self.parts_list.insert(tk.END, row)


    def add_item(self):
        if self.part_text.get() == '' or self.customer_text.get() == '' or self.retailer_text.get() == '' or self.price_text.get() == '':
            messagebox.showerror(
                "Required Fields", "Please include all fields")
            return
        print(self.part_text.get())

        db.insert(self.part_text.get(), self.customer_text.get(),
                  self.retailer_text.get(), self.price_text.get())

        self.parts_list.delete(0, tk.END)

        self.parts_list.insert(tk.END, (self.part_text.get(), self.customer_text.get(
        ), self.retailer_text.get(), self.price_text.get()))
        self.clear_text()
        self.populate_list()


    def select_item(self, event):

        try:
            index = self.parts_list.curselection()[0]

            self.selected_item = self.parts_list.get(index)

            self.part_entry.delete(0, tk.END)
            self.part_entry.insert(tk.END, self.selected_item[1])
            self.customer_entry.delete(0, tk.END)
            self.customer_entry.insert(tk.END, self.selected_item[2])
            self.retailer_entry.delete(0, tk.END)
            self.retailer_entry.insert(tk.END, self.selected_item[3])
            self.price_entry.delete(0, tk.END)
            self.price_entry.insert(tk.END, self.selected_item[4])
        except IndexError:
            pass


    def remove_item(self):
        db.remove(self.selected_item[0])
        self.clear_text()
        self.populate_list()


    def update_item(self):
        db.update(self.selected_item[0], self.part_text.get(
        ), self.customer_text.get(), self.retailer_text.get(), self.price_text.get())
        self.populate_list()


    def clear_text(self):
        self.part_entry.delete(0, tk.END)
        self.customer_entry.delete(0, tk.END)
        self.retailer_entry.delete(0, tk.END)
        self.price_entry.delete(0, tk.END)


root = tk.Tk()
app = Application(master=root)
app.mainloop()
